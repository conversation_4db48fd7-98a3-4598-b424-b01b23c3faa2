use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use async_trait::async_trait;
use lapin::{
    options::*, types::FieldTable, BasicProperties, Connection, ConnectionProperties, ExchangeKind,
};
use tracing::{error, info, warn, debug};
use serde_json::json;
use crate::telemetry::core::{TelemetryResult, TelemetryError};
use crate::telemetry::core::traits::rabbitmq::*;
use crate::telemetry::core::types::{RabbitMQEvent, RabbitMQMetrics, RabbitMQEventType};
// Update to use the re-exported AlertLevel
use crate::telemetry::AlertLevel;
use crate::config;

/// Generic RabbitMQ connection handler that implements connection management functionality
pub struct GenericRabbitMQConnector {
    config: Arc<config::Settings>,
    connection: Arc<Mutex<Option<Connection>>>,
    last_connection_attempt: Arc<Mutex<Option<Instant>>>,
    connection_retry_delay: Duration,
    connection_timeout: Duration,
}

impl GenericRabbitMQConnector {
    pub fn new(config: Arc<config::Settings>) -> Self {
        Self {
            config,
            connection: Arc::new(Mutex::new(None)),
            last_connection_attempt: Arc::new(Mutex::new(None)),
            connection_retry_delay: Duration::from_secs(5),
            connection_timeout: Duration::from_secs(30),
        }
    }

    pub fn with_retry_delay(mut self, delay_secs: u64) -> Self {
        self.connection_retry_delay = Duration::from_secs(delay_secs);
        self
    }

    pub fn with_timeout(mut self, timeout_secs: u64) -> Self {
        self.connection_timeout = Duration::from_secs(timeout_secs);
        self
    }

    async fn get_connection_url(&self) -> String {
        // Handle optional RabbitMQ config
        match &self.config.telemetry.rabbitmq {
            Some(rabbitmq_config) => {
                format!(
                    "amqp://{}:{}@{}:{}/{}",
                    rabbitmq_config.username,
                    rabbitmq_config.password,
                    rabbitmq_config.host,
                    rabbitmq_config.port,
                    rabbitmq_config.vhost.trim_start_matches('/')
                )
            }
            None => {
                // Return a default URL that will fail connection (since RabbitMQ is not configured)
                "amqp://guest:guest@localhost:5672/".to_string()
            }
        }
    }
}

#[async_trait]
impl RabbitMQTelemetryComponent for GenericRabbitMQConnector {
    async fn connect(&self) -> TelemetryResult<()> {
        let mut last_attempt = self.last_connection_attempt.lock().await;
        
        // Check if we need to respect the retry delay
        if let Some(attempt_time) = *last_attempt {
            let elapsed = attempt_time.elapsed();
            if elapsed < self.connection_retry_delay {
                return Err(TelemetryError::ConnectionError(format!(
                    "Connection retry too soon. Please wait {:?} before retrying", 
                    self.connection_retry_delay - elapsed
                )));
            }
        }
        
        *last_attempt = Some(Instant::now());
        
        // Check if RabbitMQ is configured
        let rabbitmq_config = match &self.config.telemetry.rabbitmq {
            Some(config) => config,
            None => {
                info!("RabbitMQ not configured for telemetry - skipping connection");
                return Ok(()); // Skip connection for basic telemetry
            }
        };

        let addr = self.get_connection_url().await;

        info!("Connecting to RabbitMQ at {}", addr);

        match tokio::time::timeout(
            self.connection_timeout,
            Connection::connect(
                &addr,
                ConnectionProperties::default()
                    .with_connection_name(rabbitmq_config.connection_name.clone().into())
                    .with_executor(tokio_executor_trait::Tokio::current()),
            )
        ).await {
            Ok(conn_result) => {
                match conn_result {
                    Ok(conn) => {
                        let mut conn_guard = self.connection.lock().await;
                        *conn_guard = Some(conn);
                        info!("Successfully connected to RabbitMQ");
                        Ok(())
                    },
                    Err(e) => {
                        error!("Failed to connect to RabbitMQ: {}", e);
                        Err(TelemetryError::ConnectionError(format!("Failed to connect to RabbitMQ: {}", e)))
                    }
                }
            },
            Err(_) => {
                error!("Connection timeout while connecting to RabbitMQ");
                Err(TelemetryError::ConnectionTimeout("Connection to RabbitMQ timed out".to_string()))
            }
        }
    }

    async fn disconnect(&self) -> TelemetryResult<()> {
        let mut conn_guard = self.connection.lock().await;
        if let Some(conn) = conn_guard.take() {
            info!("Disconnecting from RabbitMQ");
            match conn.close(0, "Normal shutdown").await {
                Ok(_) => {
                    info!("Successfully disconnected from RabbitMQ");
                    Ok(())
                },
                Err(e) => {
                    error!("Error while disconnecting from RabbitMQ: {}", e);
                    Err(TelemetryError::ConnectionError(format!("Error while disconnecting: {}", e)))
                }
            }
        } else {
            debug!("No active connection to disconnect");
            Ok(())
        }
    }

    fn is_connected(&self) -> bool {
        // This is a synchronous method, so we can't do async lock
        // We need to rely on other mechanisms to check connection status
        // This is a simplified implementation
        true
    }
}

/// Generic RabbitMQ queue manager for declaring and binding queues
pub struct GenericRabbitMQQueueManager {
    connector: Arc<GenericRabbitMQConnector>,
    config: Arc<config::Settings>,
}

impl GenericRabbitMQQueueManager {
    pub fn new(connector: Arc<GenericRabbitMQConnector>, config: Arc<config::Settings>) -> Self {
        Self {
            connector,
            config,
        }
    }
    
    pub async fn declare_exchange(
        &self, 
        exchange_name: &str,
        exchange_type: ExchangeKind,
        options: ExchangeDeclareOptions
    ) -> TelemetryResult<()> {
        if let Some(conn) = &*self.connector.connection.lock().await {
            let channel = conn.create_channel().await
                .map_err(|e| TelemetryError::OperationError(format!("Failed to create channel: {}", e)))?;
            
            channel.exchange_declare(
                exchange_name,
                exchange_type,
                options,
                FieldTable::default()
            ).await
                .map_err(|e| TelemetryError::OperationError(format!("Failed to declare exchange: {}", e)))?;
            
            Ok(())
        } else {
            Err(TelemetryError::NotConnected("Not connected to RabbitMQ".to_string()))
        }
    }
    
    pub async fn declare_queue(
        &self, 
        queue_name: &str,
        options: QueueDeclareOptions
    ) -> TelemetryResult<()> {
        if let Some(conn) = &*self.connector.connection.lock().await {
            let channel = conn.create_channel().await
                .map_err(|e| TelemetryError::OperationError(format!("Failed to create channel: {}", e)))?;
            
            channel.queue_declare(
                queue_name,
                options,
                FieldTable::default()
            ).await
                .map_err(|e| TelemetryError::OperationError(format!("Failed to declare queue: {}", e)))?;
            
            Ok(())
        } else {
            Err(TelemetryError::NotConnected("Not connected to RabbitMQ".to_string()))
        }
    }
    
    pub async fn bind_queue(
        &self,
        queue_name: &str,
        exchange_name: &str,
        routing_key: &str,
    ) -> TelemetryResult<()> {
        if let Some(conn) = &*self.connector.connection.lock().await {
            let channel = conn.create_channel().await
                .map_err(|e| TelemetryError::OperationError(format!("Failed to create channel: {}", e)))?;
            
            channel.queue_bind(
                queue_name,
                exchange_name,
                routing_key,
                QueueBindOptions::default(),
                FieldTable::default()
            ).await
                .map_err(|e| TelemetryError::OperationError(format!("Failed to bind queue: {}", e)))?;
            
            Ok(())
        } else {
            Err(TelemetryError::NotConnected("Not connected to RabbitMQ".to_string()))
        }
    }
}

/// Generic base implementation for RabbitMQ health checks
pub struct GenericRabbitMQHealthChecker {
    connector: Arc<GenericRabbitMQConnector>,
}

impl GenericRabbitMQHealthChecker {
    pub fn new(connector: Arc<GenericRabbitMQConnector>) -> Self {
        Self {
            connector,
        }
    }
}

#[async_trait]
impl RabbitMQHealthCheck for GenericRabbitMQHealthChecker {
    async fn check_connection(&self) -> TelemetryResult<bool> {
        if let Some(conn) = &*self.connector.connection.lock().await {
            // Try creating and closing a channel as a health check
            match conn.create_channel().await {
                Ok(channel) => {
                    // If we can open and close a channel, the connection is healthy
                    let _ = channel.close(0, "Health check").await;
                    Ok(true)
                },
                Err(_) => Ok(false)
            }
        } else {
            Ok(false)
        }
    }

    async fn check_queues(&self, queue_names: &[String]) -> TelemetryResult<HashMap<String, bool>> {
        let mut results = HashMap::new();
        
        if let Some(conn) = &*self.connector.connection.lock().await {
            let channel = match conn.create_channel().await {
                Ok(ch) => ch,
                Err(_) => return Err(TelemetryError::OperationError("Failed to create channel".to_string())),
            };
            
            for queue_name in queue_names {
                match channel.queue_declare(
                    queue_name,
                    QueueDeclareOptions {
                        passive: true,
                        ..Default::default()
                    },
                    FieldTable::default(),
                ).await {
                    Ok(_) => { results.insert(queue_name.clone(), true); },
                    Err(_) => { results.insert(queue_name.clone(), false); }
                }
            }
        } else {
            // If not connected, all queues are considered unavailable
            for queue_name in queue_names {
                results.insert(queue_name.clone(), false);
            }
        }
        
        Ok(results)
    }

    async fn get_health_status(&self) -> TelemetryResult<HashMap<String, String>> {
        let mut status = HashMap::new();
        
        // Check connection
        let connected = self.check_connection().await?;
        status.insert("connection".to_string(), if connected { "connected".to_string() } else { "disconnected".to_string() });
        
        // Add more detailed health checks here
        if connected {
            if let Some(conn) = &*self.connector.connection.lock().await {
                // Get connection details if available
                status.insert("status".to_string(), "healthy".to_string());
            }
        } else {
            status.insert("status".to_string(), "unhealthy".to_string());
        }
        
        Ok(status)
    }
}
