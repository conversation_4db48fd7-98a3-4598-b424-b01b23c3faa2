use crate::telemetry::core::{
    TelemetryResult,
    types::{RabbitMQEvent, RabbitMQEventType, RabbitMQMetrics},
    config::TelemetryRabbitMQConfig,
};
use opentelemetry::metrics::{Counter, Meter};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};

pub struct RabbitMQTelemetry {
    config: TelemetryRabbitMQConfig,
    metrics: Arc<RabbitMQMetricsCollector>,
    tracer: opentelemetry::global::BoxedTracer,
}

struct RabbitMQMetricsCollector {
    connections_counter: Counter<u64>,
    channels_counter: Counter<u64>,
    queues_counter: Counter<u64>,
    exchanges_counter: Counter<u64>,
    consumers_counter: Counter<u64>,
    messages_published_counter: Counter<u64>,
    messages_delivered_counter: Counter<u64>,
    messages_acknowledged_counter: Counter<u64>,
    messages_rejected_counter: Counter<u64>,
    connections_value: AtomicU64,
    channels_value: AtomicU64,
    queues_value: AtomicU64,
    exchanges_value: AtomicU64,
    consumers_value: AtomicU64,
    messages_published_value: AtomicU64,
    messages_delivered_value: AtomicU64,
    messages_acknowledged_value: AtomicU64,
    messages_rejected_value: AtomicU64,
}

impl RabbitMQTelemetry {
    pub fn new(config: &TelemetryRabbitMQConfig) -> TelemetryResult<Self> {
        let meter = opentelemetry::global::meter("rabbitmq");
        let metrics = Arc::new(RabbitMQMetricsCollector::new(&meter)?);
        let tracer = opentelemetry::global::tracer("rabbitmq");

        Ok(Self {
            config: config.clone(),
            metrics,
            tracer,
        })
    }

    pub fn init(&mut self) -> TelemetryResult<()> {
        Ok(())
    }

    pub fn shutdown(&self) {}

    pub fn record_event(&self, event: RabbitMQEvent) {
        match event.event_type {
            RabbitMQEventType::ConnectionCreated => {
                self.metrics.connections_counter.add(1, &[]);
                self.metrics.connections_value.fetch_add(1, Ordering::SeqCst);
            }
            RabbitMQEventType::MessagePublished => {
                self.metrics.messages_published_counter.add(1, &[]);
                self.metrics.messages_published_value.fetch_add(1, Ordering::SeqCst);
            }
            RabbitMQEventType::MessageDelivered => {
                self.metrics.messages_delivered_counter.add(1, &[]);
                self.metrics.messages_delivered_value.fetch_add(1, Ordering::SeqCst);
            }
            RabbitMQEventType::MessageAcknowledged => {
                self.metrics.messages_acknowledged_counter.add(1, &[]);
                self.metrics.messages_acknowledged_value.fetch_add(1, Ordering::SeqCst);
            }
            RabbitMQEventType::MessageRejected => {
                self.metrics.messages_rejected_counter.add(1, &[]);
                self.metrics.messages_rejected_value.fetch_add(1, Ordering::SeqCst);
            }
            _ => {}
        }
    }

    pub fn get_metrics(&self) -> RabbitMQMetrics {
        RabbitMQMetrics {
            connections_total: self.metrics.connections_value.load(Ordering::SeqCst),
            channels_total: self.metrics.channels_value.load(Ordering::SeqCst),
            queues_total: self.metrics.queues_value.load(Ordering::SeqCst),
            exchanges_total: self.metrics.exchanges_value.load(Ordering::SeqCst),
            consumers_total: self.metrics.consumers_value.load(Ordering::SeqCst),
            messages_published_total: self.metrics.messages_published_value.load(Ordering::SeqCst),
            messages_delivered_total: self.metrics.messages_delivered_value.load(Ordering::SeqCst),
            messages_acknowledged_total: self.metrics.messages_acknowledged_value.load(Ordering::SeqCst),
            messages_rejected_total: self.metrics.messages_rejected_value.load(Ordering::SeqCst),
        }
    }
}

impl RabbitMQMetricsCollector {
    fn new(meter: &Meter) -> TelemetryResult<Self> {
        Ok(Self {
            connections_counter: meter.u64_counter("rabbitmq_connections_total")
                .with_description("Total number of RabbitMQ connections")
                .build(),
            channels_counter: meter.u64_counter("rabbitmq_channels_total")
                .with_description("Total number of RabbitMQ channels")
                .build(),
            queues_counter: meter.u64_counter("rabbitmq_queues_total")
                .with_description("Total number of RabbitMQ queues")
                .build(),
            exchanges_counter: meter.u64_counter("rabbitmq_exchanges_total")
                .with_description("Total number of RabbitMQ exchanges")
                .build(),
            consumers_counter: meter.u64_counter("rabbitmq_consumers_total")
                .with_description("Total number of RabbitMQ consumers")
                .build(),
            messages_published_counter: meter.u64_counter("rabbitmq_messages_published_total")
                .with_description("Total number of messages published")
                .build(),
            messages_delivered_counter: meter.u64_counter("rabbitmq_messages_delivered_total")
                .with_description("Total number of messages delivered")
                .build(),
            messages_acknowledged_counter: meter.u64_counter("rabbitmq_messages_acknowledged_total")
                .with_description("Total number of messages acknowledged")
                .build(),
            messages_rejected_counter: meter.u64_counter("rabbitmq_messages_rejected_total")
                .with_description("Total number of messages rejected")
                .build(),
            connections_value: AtomicU64::new(0),
            channels_value: AtomicU64::new(0),
            queues_value: AtomicU64::new(0),
            exchanges_value: AtomicU64::new(0),
            consumers_value: AtomicU64::new(0),
            messages_published_value: AtomicU64::new(0),
            messages_delivered_value: AtomicU64::new(0),
            messages_acknowledged_value: AtomicU64::new(0),
            messages_rejected_value: AtomicU64::new(0),
        })
    }
}
