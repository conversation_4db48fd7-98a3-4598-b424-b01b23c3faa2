pub mod config; // Already public, no change needed, but confirming
pub mod types;
pub mod traits; // Add this line to expose the traits module
pub mod llm;
mod rabbitmq;
mod storage;
pub mod error;

pub use config::{TelemetryConfig, DynamicConfig, Settings};
pub use error::{TelemetryError, TelemetryResult};
pub use rabbitmq::RabbitMQTelemetry;
pub use storage::SurrealDBTelemetry;
pub use llm::LLMTelemetry;

use tracing_subscriber::{EnvFilter, fmt, layer::SubscriberExt, util::SubscriberInitExt};
use opentelemetry::{
    global,
    KeyValue,
    trace::{Tracer, Span},
};
use opentelemetry_sdk::{
    trace::{self, Sampler, SdkTracerProvider},
    resource::{ResourceDetector, EnvResourceDetector, TelemetryResourceDetector},
    metrics::SdkMeterProvider,
};
use prometheus::{TextEncoder, Registry};
use tracing::info;
use std::sync::{Arc, RwLock};
use tokio::sync::broadcast;
use tokio::time::{Duration, interval};
use log::{info as log_info, error};
use std::time::Duration as StdDuration;

pub struct TelemetryCore {
    config: TelemetryConfig,
    registry: Registry,
    encoder: TextEncoder,
    tracer_provider: Option<SdkTracerProvider>,
    rabbitmq: Option<RabbitMQTelemetry>,
    surrealdb: SurrealDBTelemetry,
    llm: LLMTelemetry,
}

impl TelemetryCore {
    pub fn new(config: TelemetryConfig) -> TelemetryResult<Self> {
        // RabbitMQ is now optional - only initialize if configured
        let rabbitmq = if let Some(ref rabbitmq_config) = config.rabbitmq {
            Some(RabbitMQTelemetry::new(rabbitmq_config)?)
        } else {
            None
        };

        let tracer_provider = SdkTracerProvider::default();
        let meter_provider = Arc::new(SdkMeterProvider::default());
        let settings = Arc::new(Settings::default());

        let surrealdb = SurrealDBTelemetry::new(
            Arc::new(tracer_provider.clone()),
            meter_provider.clone(),
            settings,
        )?;

        let llm = LLMTelemetry::new(
            config.llm.clone(),
            Arc::new(tracer_provider.clone()),
            Arc::new(Registry::new()),
        )?;

        Ok(Self {
            config,
            registry: Registry::new(),
            encoder: TextEncoder::new(),
            tracer_provider: Some(tracer_provider),
            rabbitmq,
            surrealdb,
            llm,
        })
    }

    pub fn init(&mut self) -> TelemetryResult<()> {
        // Initialize RabbitMQ only if configured
        if let Some(ref mut rabbitmq) = self.rabbitmq {
            rabbitmq.init()?;
            info!("RabbitMQ telemetry initialized");
        } else {
            info!("RabbitMQ telemetry disabled - using direct cluster communication");
        }
        
        // Set service name and attributes via environment variables
        std::env::set_var("OTEL_SERVICE_NAME", "prisma_ai");
        std::env::set_var("OTEL_RESOURCE_ATTRIBUTES", "service.namespace=telemetry");
        
        let tracer_provider = SdkTracerProvider::builder()
            .with_sampler(Sampler::AlwaysOn)
            .build();

        global::set_tracer_provider(tracer_provider.clone());
        self.tracer_provider = Some(tracer_provider);

        tracing_subscriber::registry()
            .with(EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| EnvFilter::new("info")))
            .with(fmt::layer())
            .init();

        info!("Telemetry core initialized");
        
        Ok(())
    }

    pub fn shutdown(&self) {
        // Shutdown RabbitMQ only if it was initialized
        if let Some(ref rabbitmq) = self.rabbitmq {
            rabbitmq.shutdown();
        }
        // Properly shutdown the tracer provider
        if let Some(provider) = &self.tracer_provider {
            provider.shutdown();
        }
    }

    pub fn get_metrics(&self) -> String {
        let metric_families = self.registry.gather();
        self.encoder.encode_to_string(&metric_families).unwrap_or_default()
    }

    pub fn create_span(&self, name: String, attributes: Vec<KeyValue>) -> opentelemetry::global::BoxedSpan {
        let tracer = global::tracer("prisma_ai");
        let mut span = tracer.start(name);
        span.set_attributes(attributes);
        span
    }
}

pub struct TelemetryManager {
    config: Arc<RwLock<DynamicConfig>>,
    shutdown_tx: broadcast::Sender<()>,
    metrics_manager: Arc<RwLock<crate::telemetry::metrics::MetricsManager>>,
}

impl TelemetryManager {
    pub fn new(config_path: impl AsRef<std::path::Path>) -> TelemetryResult<Self> {
        let dynamic_config = DynamicConfig::new(config_path)?;
        let config = Arc::new(RwLock::new(dynamic_config));
        let (shutdown_tx, _) = broadcast::channel(1);
        
        let initial_config = {
            let config_guard = config.read().map_err(|e| TelemetryError::ConfigError(format!("Failed to read config: {}", e)))?;
            config_guard.get_config().read().map_err(|e| TelemetryError::ConfigError(format!("Failed to read config: {}", e)))?.clone()
        };

        let metrics_manager = Arc::new(RwLock::new(
            crate::telemetry::metrics::MetricsManager::new(initial_config)?
        ));

        Ok(Self {
            config,
            shutdown_tx,
            metrics_manager,
        })
    }

    pub async fn start(&self) -> TelemetryResult<()> {
        log_info!("Starting telemetry system...");
        
        // Start config reload task
        let config_clone: Arc<RwLock<DynamicConfig>> = Arc::clone(&self.config);
        let mut shutdown_rx = self.shutdown_tx.subscribe();
        let metrics_manager = Arc::clone(&self.metrics_manager);

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(30));
            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        if let Ok(mut config) = config_clone.write() {
                            if let Ok(true) = config.check_and_reload() {
                                log_info!("Configuration reloaded");
                                if let Ok(config_guard) = config.get_config().read() {
                                    if let Ok(mut metrics_mgr) = metrics_manager.write() {
                                        if let Err(e) = metrics_mgr.reload_config(config_guard.clone()) {
                                            error!("Failed to reload metrics configuration: {}", e);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    _ = shutdown_rx.recv() => {
                        log_info!("Shutting down config reload task");
                        break;
                    }
                }
            }
        });

        // Start metrics collectors
        if let Ok(metrics_mgr) = self.metrics_manager.read() {
            metrics_mgr.start_all()?;
        }

        Ok(())
    }

    pub async fn stop(&self) -> TelemetryResult<()> {
        log_info!("Stopping telemetry system...");
        
        // Signal shutdown
        let _ = self.shutdown_tx.send(());

        // Stop metrics collectors
        if let Ok(metrics_mgr) = self.metrics_manager.read() {
            metrics_mgr.stop_all()?;
        }

        Ok(())
    }

    pub fn reload_config(&self) -> TelemetryResult<()> {
        if let Ok(mut config) = self.config.write() {
            config.force_reload()?;
            
            if let Ok(config_guard) = config.get_config().read() {
                if let Ok(mut metrics_mgr) = self.metrics_manager.write() {
                    metrics_mgr.reload_config(config_guard.clone())?;
                }
            }
        }
        Ok(())
    }
}

