use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use std::time::SystemTime;
use tracing::Level;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentSettings {
    pub service_name: String,
    pub environment: String,
    pub service_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Settings {
    pub global: GlobalConfig,
    pub database: DatabaseConfig,
    pub rabbitmq: RabbitMQConfig,
    pub telemetry: TelemetryConfig,
}

/// Database connection configuration (migrated from config.yaml)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub namespace: String,
    pub database: String,
    pub use_http: bool,
    pub version: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GlobalConfig {
    pub service_name: String,
    pub environment: String,
    pub service_id: String,
    pub endpoints: GlobalEndpoints,
    pub api_keys: GlobalApiKeys,
    pub alerts: GlobalAlerts,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalEndpoints {
    pub prometheus_enabled: bool,
    pub prometheus: String,
    pub grafana_enabled: bool,
    pub grafana: String,
    pub grafana_version: Option<String>,
    pub loki_enabled: bool,
    pub loki: String,
    pub loki_version: Option<String>,
}

impl Default for GlobalEndpoints {
    fn default() -> Self {
        Self {
            prometheus_enabled: true,
            prometheus: "http://*************:30158".to_string(),
            grafana_enabled: true,
            grafana: "http://*************:30455".to_string(),
            grafana_version: Some("11.4.0".to_string()),
            loki_enabled: true,
            loki: "http://*************:32278/loki/api/v1/push".to_string(),
            loki_version: Some("3.3.0".to_string()),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalApiKeys {
    pub prisma_ai_enabled: bool,
    pub slack_enabled: bool,
    pub slack_token: String,
    pub slack_channel: String,
    pub discord_enabled: bool,
    pub discord_webhook: String,
    pub gmail_enabled: bool,
    pub gmail_email: String,
    pub gmail_app_password: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalAlerts {
    pub enabled: bool,
    pub default_log_level: String,
    pub enabled_modules: Vec<String>,
    pub slack: AlertSlackConfig,
    pub discord: AlertDiscordConfig,
    pub email: AlertEmailConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertSlackConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub default_level: String,
    pub modules: Vec<String>,
    pub message_template: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertDiscordConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub default_level: String,
    pub modules: Vec<String>,
    pub message_template: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertEmailConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub default_level: String,
    pub modules: Vec<String>,
    pub subject_template: String,
    pub body_template: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub vhost: String,
    pub connection_name: String,
    pub metrics: RabbitMQMetricsConfig,
    pub telemetry: RabbitMQTelemetryConfig,
    pub alerts: RabbitMQAlertsConfig,
    pub dashboards: RabbitMQDashboardsConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQAlertsConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub alert_on_connection_issues: bool,
    pub alert_on_channel_issues: bool,
    pub alert_on_queue_issues: bool,
    pub alert_on_consumer_issues: bool,
    pub alert_on_publisher_issues: bool,
    pub thresholds: RabbitMQAlertThresholds,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQAlertThresholds {
    pub connection_timeout_ms: u64,
    pub queue_full_percentage: u8,
    pub message_rate_threshold: u64,
    pub consumer_lag_threshold: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQTelemetryConfig {
    pub connection_name: String,
    pub enable_connection_tracing: bool,
    pub enable_channel_tracing: bool,
    pub enable_queue_tracing: bool,
    pub enable_exchange_tracing: bool,
    pub enable_consumer_tracing: bool,
    pub enable_publisher_tracing: bool,
    pub sampling_rate: f64,
    pub metrics: RabbitMQMetricsConfig,
    pub logging: RabbitMQLoggingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQMetricsConfig {
    pub connection_metrics_enabled: bool,
    pub connection_latency_buckets: Vec<f64>,
    pub tls_metrics_enabled: bool,
    pub channel_metrics_enabled: bool,
    pub channel_latency_buckets: Vec<f64>,
    pub message_metrics_enabled: bool,
    pub message_size_buckets: Vec<f64>,
    pub message_processing_buckets: Vec<f64>,
    pub queue_metrics_enabled: bool,
    pub queue_size_enabled: bool,
    pub queue_memory_enabled: bool,
    pub queue_consumer_enabled: bool,
    pub queue_growth_rate_enabled: bool,
    pub exchange_metrics_enabled: bool,
    pub exchange_binding_enabled: bool,
    pub routing_metrics_enabled: bool,
    pub consumer_metrics_enabled: bool,
    pub consumer_latency_buckets: Vec<f64>,
    pub consumer_utilization_enabled: bool,
    pub consumer_lag_enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQLoggingConfig {
    #[serde(with = "level_serde")]
    pub min_level: Level,
    pub stream_name: String,
    pub components: RabbitMQLoggingComponents,
    pub labels: RabbitMQLoggingLabels,
    pub performance: RabbitMQPerformanceThresholds,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQLoggingComponents {
    pub connection: bool,
    pub channel: bool,
    pub queue: bool,
    pub exchange: bool,
    pub publish: bool,
    pub consume: bool,
    pub routing: bool,
    pub performance: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQLoggingLabels {
    pub service: String,
    pub environment: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQPerformanceThresholds {
    pub connection_warning_threshold_ms: u64,
    pub channel_warning_threshold_ms: u64,
    pub queue_warning_threshold_ms: u64,
    pub publish_warning_threshold_ms: u64,
    pub consume_warning_threshold_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQDashboardsConfig {
    pub overview_refresh: String,
    pub overview_tags: Vec<String>,
    pub connection_refresh: String,
    pub connection_tags: Vec<String>,
    pub performance_refresh: String,
    pub performance_tags: Vec<String>,
    pub queue_exchange_refresh: String,
    pub queue_exchange_tags: Vec<String>,
    pub error_refresh: String,
    pub error_tags: Vec<String>,
}

/// Telemetry-specific RabbitMQ configuration (monitoring settings only)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TelemetryRabbitMQConfig {
    pub enable_connection_metrics: bool,
    pub enable_channel_metrics: bool,
    pub enable_message_metrics: bool,
    pub sampling_rate: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TelemetryConfig {
    pub endpoints: GlobalEndpoints,
    pub env_settings: EnvironmentSettings,
    pub grafana_service_token: String,
    pub rabbitmq: RabbitMQConfig,
    pub surrealdb: SurrealDBConfig,
    pub llm: LLMConfig,
    pub system_error: SystemErrorConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBConfig {
    pub connection_name: String,
    pub enable_query_tracing: bool,
    pub enable_transaction_tracing: bool,
    pub enable_document_tracing: bool,
    pub enable_index_tracing: bool,
    pub sampling_rate: f64,
    pub logging: SurrealDBLoggingConfig,
    pub metrics: SurrealDBMetricsConfig,
    pub alerts: SurrealDBAlertsConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBAlertsConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub alert_on_slow_queries: bool,
    pub alert_on_transaction_issues: bool,
    pub alert_on_authentication_issues: bool,
    pub alert_on_performance_warnings: bool,
    pub thresholds: SurrealDBAlertThresholds,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBAlertThresholds {
    pub slow_query_ms: u64,
    pub transaction_timeout_ms: u64,
    pub connection_timeout_ms: u64,
    pub memory_usage_percentage: u8,
    pub storage_usage_percentage: u8,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBMetricsConfig {
    pub basic_metrics_enabled: bool,
    pub operation_latency_buckets: Vec<f64>,
    pub query_metrics_enabled: bool,
    pub query_latency_buckets: Vec<f64>,
    pub query_complexity_enabled: bool,
    pub query_cache_enabled: bool,
    pub query_cache_buckets: Vec<f64>,
    pub storage_metrics_enabled: bool,
    pub storage_space_enabled: bool,
    pub storage_buckets: Vec<f64>,
    pub memory_metrics_enabled: bool,
    pub memory_pressure_enabled: bool,
    pub gc_metrics_enabled: bool,
    pub memory_buckets: Vec<f64>,
    pub transaction_metrics_enabled: bool,
    pub transaction_latency_buckets: Vec<f64>,
    pub rollback_metrics_enabled: bool,
    pub lock_contention_enabled: bool,
    pub lock_buckets: Vec<f64>,
    pub rag_metrics_enabled: bool,
    pub rag_latency_buckets: Vec<f64>,
    pub embedding_metrics_enabled: bool,
    pub vector_search_enabled: bool,
    pub document_processing_enabled: bool,
    pub embedding_latency_buckets: Vec<f64>,
    pub vector_search_buckets: Vec<f64>,
    pub document_processing_buckets: Vec<f64>,
    pub document_metrics_enabled: bool,
    pub document_count_enabled: bool,
    pub index_metrics_enabled: bool,
    pub index_size_enabled: bool,
    pub index_operation_buckets: Vec<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLoggingConfig {
    #[serde(with = "level_serde")]
    pub min_level: Level,
    pub stream_name: String,
    pub components: SurrealDBLoggingComponents,
    pub labels: SurrealDBLoggingLabels,
    pub performance: SurrealDBPerformanceThresholds,
    pub loki: SurrealDBLokiConfig,
    pub query: SurrealDBQueryLogging,
    pub transaction: SurrealDBTransactionLogging,
    pub document: SurrealDBDocumentLogging,
    pub index: SurrealDBIndexLogging,
    pub rag: SurrealDBRagLogging,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLoggingComponents {
    pub query: bool,
    pub transaction: bool,
    pub document: bool,
    pub index: bool,
    pub authentication: bool,
    pub performance: bool,
    pub rag: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLoggingLabels {
    pub service: String,
    pub environment: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBPerformanceThresholds {
    pub query_warning_threshold_ms: u64,
    pub transaction_warning_threshold_ms: u64,
    pub index_warning_threshold_ms: u64,
    pub embedding_warning_threshold_ms: u64,
    pub vector_search_warning_threshold_ms: u64,
    pub document_processing_warning_threshold_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLokiConfig {
    pub endpoint: String,
    pub batch_size: usize,
    pub batch_wait_ms: u64,
    pub retention_days: u32,
    pub labels: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBQueryLogging {
    pub log_slow_queries: bool,
    pub slow_query_threshold_ms: u64,
    pub log_query_plans: bool,
    pub log_query_cache_events: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBTransactionLogging {
    pub log_begin_commit: bool,
    pub log_rollbacks: bool,
    pub log_deadlocks: bool,
    pub log_lock_wait_time: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBDocumentLogging {
    pub log_mutations: bool,
    pub log_batch_operations: bool,
    pub log_validation_errors: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBIndexLogging {
    pub log_builds: bool,
    pub log_updates: bool,
    pub log_analysis: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBRagLogging {
    pub log_embeddings: bool,
    pub log_vector_searches: bool,
    pub log_document_processing: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBDashboardsConfig {
    pub overview_refresh: String,
    pub overview_tags: Vec<String>,
    pub performance_refresh: String,
    pub performance_tags: Vec<String>,
    pub query_refresh: String,
    pub query_tags: Vec<String>,
    pub storage_refresh: String,
    pub storage_tags: Vec<String>,
    pub rag_refresh: String,
    pub rag_tags: Vec<String>,
    pub error_refresh: String,
    pub error_tags: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    pub enable_inference_tracing: bool,
    pub enable_lora_tracing: bool,
    pub enable_embedding_tracing: bool,
    pub sampling_rate: f64,
    pub tracing: LLMTracingConfig,
    pub metrics: LLMMetricsConfig,
    pub logging: LLMLoggingConfig,
    pub dashboards: LLMDashboardConfig,
    pub alerts: LLMAlertConfig,  // Add this field
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMTracingConfig {
    pub model_loading: bool,
    pub inference: bool,
    pub tokenization: bool,
    pub embedding: bool,
    pub lora: bool,
    pub attributes: LLMTracingAttributes,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMTracingAttributes {
    pub service: String,
    pub environment: String,
}

impl Default for LLMTracingAttributes {
    fn default() -> Self {
        Self {
            service: "prisma_ai".to_string(),
            environment: "development".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMMetricsConfig {
    pub model_performance_enabled: bool,
    pub resource_usage_enabled: bool,
    pub lora_metrics_enabled: bool,
    pub embedding_metrics_enabled: bool,
    pub inference_latency_buckets: Vec<f64>,
    pub batch_size_buckets: Vec<f64>,
    pub memory_usage_buckets: Vec<f64>,
    pub tokens_per_second_buckets: Vec<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMLoggingConfig {
    pub min_level: String,
    pub stream_name: String,
    pub components: LLMLoggingComponents,
    pub labels: LLMLoggingLabels,
    pub performance: LLMLoggingPerformance,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMLoggingComponents {
    pub inference: bool,
    pub lora: bool,
    pub embedding: bool,
    pub tokenization: bool,
    pub performance: bool,
    pub resources: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMLoggingLabels {
    pub service: String,
    pub environment: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMLoggingPerformance {
    pub inference_warning_threshold_ms: u64,
    pub lora_warning_threshold_ms: u64,
    pub embedding_warning_threshold_ms: u64,
    pub tokenization_warning_threshold_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMDashboardConfig {
    pub overview_refresh: String,
    pub overview_tags: Vec<String>,
    pub model_performance_refresh: String,
    pub model_performance_tags: Vec<String>,
    pub resource_usage_refresh: String,
    pub resource_usage_tags: Vec<String>,
    pub lora_refresh: String,
    pub lora_tags: Vec<String>,
    pub embedding_refresh: String,
    pub embedding_tags: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMAlertConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub performance: LLMPerformanceAlerts,
    pub errors: LLMErrorAlerts,
    pub resources: LLMResourceAlerts,
    pub quality: LLMQualityAlerts,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMPerformanceAlerts {
    pub enabled: bool,
    pub high_latency_threshold_ms: u64,
    pub low_throughput_threshold_tokens: u64,
    pub resource_exhaustion_percentage: u8,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMErrorAlerts {
    pub enabled: bool,
    pub alert_on_model_loading: bool,
    pub alert_on_inference_failures: bool,
    pub alert_on_out_of_memory: bool,
    pub max_consecutive_failures: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMResourceAlerts {
    pub enabled: bool,
    pub gpu_memory_threshold_percentage: u8,
    pub cpu_utilization_threshold_percentage: u8,
    pub system_memory_threshold_percentage: u8,
    pub monitoring_interval_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMQualityAlerts {
    pub enabled: bool,
    pub token_error_threshold_percentage: f32,
    pub embedding_quality_threshold: f32,
    pub context_window_usage_percentage: u8,
    pub max_invalid_tokens_per_batch: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorConfig {
    pub enabled: bool,
    pub logging: SystemErrorLoggingConfig,
    pub metrics: SystemErrorMetricsConfig,
    pub alerts: SystemErrorAlertsConfig,
    pub tracing: SystemErrorTracingConfig,
    pub dashboards: SystemErrorDashboardsConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorLoggingConfig {
    pub min_level: String,
    pub include_stacktrace: bool,
    pub include_metadata: bool,
    pub retention_days: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorMetricsConfig {
    pub enabled: bool,
    pub error_rate_window_minutes: u32,
    pub max_stored_errors: usize,
    pub histogram_buckets: Vec<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorAlertsConfig {
    pub enabled: bool,
    pub alert_on_critical: bool,
    pub alert_on_error: bool,
    pub alert_on_warning: bool,
    pub error_rate_threshold: f64,
    pub error_burst_threshold: u32,
    pub error_burst_window_seconds: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorTracingConfig {
    pub enabled: bool,
    pub include_context: bool,
    pub include_stack_trace: bool,
    pub propagate_correlation_id: bool,
    pub sampling_rate: f64,
    pub error_group_window_seconds: u64,
    pub circuit_breaker_events: bool,
    pub retry_events: bool,
    pub timeout_events: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorDashboardsConfig {
    pub enabled: bool,
    pub refresh_interval: String,
    pub error_dashboard_tags: Vec<String>,
    pub circuit_breakers_dashboard_tags: Vec<String>,
    pub retries_dashboard_tags: Vec<String>,
    pub error_distribution_dashboard_tags: Vec<String>,
    pub error_timeline_dashboard_tags: Vec<String>,
    pub error_severity_distribution_refresh: String,
    pub error_source_distribution_refresh: String,
    pub error_rate_timeline_refresh: String,
    pub circuit_breaker_status_refresh: String,
    pub retry_count_refresh: String,
    pub service_health_refresh: String,
    pub error_counts_by_service_refresh: String,
    pub error_counts_by_type_refresh: String,
    pub top_errors_refresh: String,
}

// Serde module for Level serialization/deserialization
mod level_serde {
    use serde::{Deserialize, Deserializer, Serializer};
    use tracing::Level;

    pub fn serialize<S>(level: &Level, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(&level.to_string())
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Level, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        match s.to_uppercase().as_str() {
            "ERROR" => Ok(Level::ERROR),
            "WARN" => Ok(Level::WARN),
            "INFO" => Ok(Level::INFO),
            "DEBUG" => Ok(Level::DEBUG),
            "TRACE" => Ok(Level::TRACE),
            _ => Ok(Level::INFO), // Default to INFO if invalid
        }
    }
}

// Add a wrapper struct to handle reloading
pub struct DynamicConfig {
    config: Arc<RwLock<TelemetryConfig>>,
    config_path: PathBuf,
    last_modified: SystemTime,
}

impl DynamicConfig {
    pub fn new(config_path: impl AsRef<Path>) -> Result<Self, Box<dyn std::error::Error>> {
        let config_path = config_path.as_ref().to_path_buf();
        let config = TelemetryConfig::load_from_file(&config_path)?;
        let last_modified = fs::metadata(&config_path)?.modified()?;
        
        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            config_path,
            last_modified,
        })
    }

    // Get a reference to the current config
    pub fn get_config(&self) -> Arc<RwLock<TelemetryConfig>> {
        self.config.clone()
    }

    // Check if config file has been modified and reload if necessary
    pub fn check_and_reload(&mut self) -> Result<bool, Box<dyn std::error::Error>> {
        let metadata = fs::metadata(&self.config_path)?;
        let modified = metadata.modified()?;

        if modified > self.last_modified {
            let new_config = TelemetryConfig::load_from_file(&self.config_path)?;
            if let Ok(mut config) = self.config.write() {
                *config = new_config;
                self.last_modified = modified;
                return Ok(true);
            }
        }
        Ok(false)
    }

    // Force reload the configuration regardless of file modification time
    pub fn force_reload(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let new_config = TelemetryConfig::load_from_file(&self.config_path)?;
        if let Ok(mut config) = self.config.write() {
            *config = new_config;
            self.last_modified = fs::metadata(&self.config_path)?.modified()?;
        }
        Ok(())
    }
}

impl Default for EnvironmentSettings {
    fn default() -> Self {
        Self {
            service_name: "prisma-pipeline".to_string(),
            environment: "development".to_string(),
            service_id: "sa-1-prisma-pipeline".to_string(),
        }
    }
}

impl TelemetryConfig {
    pub fn new(
        prometheus_endpoint: String,
        prometheus_enabled: bool,
        loki_endpoint: String,
        loki_enabled: bool,
        grafana_endpoint: String,
        grafana_enabled: bool,
        env_settings: EnvironmentSettings,
        grafana_service_token: String,
        rabbitmq: RabbitMQConfig,
        surrealdb: SurrealDBConfig,
    ) -> Self {
        Self {
            endpoints: GlobalEndpoints {
                prometheus_enabled,
                prometheus: prometheus_endpoint,
                loki_enabled,
                loki: loki_endpoint,
                loki_version: Some("3.3.0".to_string()),
                grafana_enabled,
                grafana: grafana_endpoint,
                grafana_version: Some("11.4.0".to_string()),
            },
            env_settings,
            grafana_service_token,
            rabbitmq,
            surrealdb,
            llm: LLMConfig::default(),
            system_error: SystemErrorConfig::default(), // Add this line
        }
    }

    // Load configuration from a TOML file
    pub fn load_from_file(path: impl AsRef<Path>) -> Result<Self, Box<dyn std::error::Error>> {
        let contents = fs::read_to_string(path)?;
        let config: Self = toml::from_str(&contents)?;
        Ok(config)
    }
}

impl Default for TelemetryConfig {
    fn default() -> Self {
        Self {
            endpoints: GlobalEndpoints::default(),
            env_settings: EnvironmentSettings::default(),
            grafana_service_token: "glsa_Go16KlrwTtqtNmeymEg56hIysMVEHyVg_8be567e3".to_string(),
            rabbitmq: RabbitMQConfig::default(),
            surrealdb: SurrealDBConfig::default(),
            llm: LLMConfig::default(),
            system_error: SystemErrorConfig::default(),
        }
    }
}

impl Default for Settings {
    fn default() -> Self {
        Self {
            global: GlobalConfig::default(),
            database: DatabaseConfig::default(),
            rabbitmq: RabbitMQConfig::default(),
            telemetry: TelemetryConfig::default(),
        }
    }
}

impl DatabaseConfig {
    /// Get database connection string
    pub fn connection_string(&self) -> String {
        if self.use_http {
            format!("http://{}:{}", self.host, self.port)
        } else {
            format!("ws://{}:{}", self.host, self.port)
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 8000,
            username: "root".to_string(),
            password: "root".to_string(),
            namespace: "prisma".to_string(),
            database: "prisma".to_string(),
            use_http: true,
            version: "2.2.1".to_string(),
        }
    }
}

impl Default for GlobalConfig {
    fn default() -> Self {
        Self {
            service_name: "prisma-pipeline".to_string(),
            environment: "development".to_string(),
            service_id: "sa-1-prisma-pipeline".to_string(),
            endpoints: GlobalEndpoints::default(),
            api_keys: GlobalApiKeys::default(),
            alerts: GlobalAlerts::default(),
        }
    }
}

impl Default for RabbitMQConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 5672,
            username: "guest".to_string(),
            password: "guest".to_string(),
            vhost: "/".to_string(),
            connection_name: "prisma_alerts".to_string(),
            metrics: RabbitMQMetricsConfig::default(),
            telemetry: RabbitMQTelemetryConfig::default(),
            alerts: RabbitMQAlertsConfig::default(),
            dashboards: RabbitMQDashboardsConfig::default(),
        }
    }
}

impl Default for GlobalApiKeys {
    fn default() -> Self {
        Self {
            prisma_ai_enabled: false,
            slack_enabled: false,
            slack_token: String::new(),
            slack_channel: String::new(),
            discord_enabled: false,
            discord_webhook: String::new(),
            gmail_enabled: false,
            gmail_email: String::new(),
            gmail_app_password: String::new(),
        }
    }
}

impl Default for GlobalAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            default_log_level: "INFO".to_string(),
            enabled_modules: vec!["rabbitmq".to_string(), "llm".to_string(), "storage".to_string()],
            slack: AlertSlackConfig::default(),
            discord: AlertDiscordConfig::default(),
            email: AlertEmailConfig::default(),
        }
    }
}

impl Default for AlertSlackConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string(), "DEBUG".to_string(), "TRACE".to_string()],
            default_level: "INFO".to_string(),
            modules: vec!["rabbitmq".to_string(), "llm".to_string(), "storage".to_string()],
            message_template: ":bell: *{level}* in module *{module}*:\n>{message}".to_string(),
        }
    }
}

impl Default for AlertDiscordConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string(), "DEBUG".to_string(), "TRACE".to_string()],
            default_level: "INFO".to_string(),
            modules: vec!["rabbitmq".to_string(), "llm".to_string(), "storage".to_string()],
            message_template: "**{level}** in module **{module}**:\n> {message}".to_string(),
        }
    }
}

impl Default for AlertEmailConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string(), "DEBUG".to_string(), "TRACE".to_string()],
            default_level: "INFO".to_string(),
            modules: vec!["rabbitmq".to_string(), "llm".to_string(), "storage".to_string()],
            subject_template: "Prisma AI Alert: {level} in {module}".to_string(),
            body_template: "Level: {level}\nModule: {module}\nTimestamp: {timestamp}\n\nMessage:\n{message}".to_string(),
        }
    }
}

impl Default for SystemErrorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            logging: SystemErrorLoggingConfig::default(),
            metrics: SystemErrorMetricsConfig::default(),
            alerts: SystemErrorAlertsConfig::default(),
            tracing: SystemErrorTracingConfig::default(),
            dashboards: SystemErrorDashboardsConfig::default(),
        }
    }
}

impl Default for SystemErrorLoggingConfig {
    fn default() -> Self {
        Self {
            min_level: "INFO".to_string(),
            include_stacktrace: true,
            include_metadata: true,
            retention_days: 30,
        }
    }
}

impl Default for SystemErrorMetricsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            error_rate_window_minutes: 60,
            max_stored_errors: 1000,
            histogram_buckets: vec![0.1, 0.5, 1.0, 2.0, 5.0],
        }
    }
}

impl Default for SystemErrorAlertsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            alert_on_critical: true,
            alert_on_error: true,
            alert_on_warning: false,
            error_rate_threshold: 10.0,
            error_burst_threshold: 5,
            error_burst_window_seconds: 60,
        }
    }
}

impl Default for RabbitMQAlertsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string(), "DEBUG".to_string(), "TRACE".to_string()],
            alert_on_connection_issues: true,
            alert_on_channel_issues: true,
            alert_on_queue_issues: true,
            alert_on_consumer_issues: true,
            alert_on_publisher_issues: true,
            thresholds: RabbitMQAlertThresholds::default(),
        }
    }
}

impl Default for RabbitMQAlertThresholds {
    fn default() -> Self {
        Self {
            connection_timeout_ms: 5000,
            queue_full_percentage: 80,
            message_rate_threshold: 1000,
            consumer_lag_threshold: 100,
        }
    }
}

impl Default for RabbitMQTelemetryConfig {
    fn default() -> Self {
        Self {
            connection_name: "prisma_ai_rabbitmq".to_string(),
            enable_connection_tracing: true,
            enable_channel_tracing: true,
            enable_queue_tracing: true,
            enable_exchange_tracing: true,
            enable_consumer_tracing: true,
            enable_publisher_tracing: true,
            sampling_rate: 1.0,
            metrics: RabbitMQMetricsConfig::default(),
            logging: RabbitMQLoggingConfig::default(),
        }
    }
}

impl Default for RabbitMQLoggingConfig {
    fn default() -> Self {
        Self {
            min_level: Level::INFO,
            stream_name: "rabbitmq".to_string(),
            components: RabbitMQLoggingComponents::default(),
            labels: RabbitMQLoggingLabels::default(),
            performance: RabbitMQPerformanceThresholds::default(),
        }
    }
}

impl Default for RabbitMQLoggingComponents {
    fn default() -> Self {
        Self {
            connection: true,
            channel: true,
            queue: true,
            exchange: true,
            publish: true,
            consume: true,
            routing: true,
            performance: true,
        }
    }
}

impl Default for RabbitMQLoggingLabels {
    fn default() -> Self {
        Self {
            service: "rabbitmq".to_string(),
            environment: "development".to_string(),
        }
    }
}

impl Default for RabbitMQPerformanceThresholds {
    fn default() -> Self {
        Self {
            connection_warning_threshold_ms: 1000,
            channel_warning_threshold_ms: 500,
            queue_warning_threshold_ms: 200,
            publish_warning_threshold_ms: 100,
            consume_warning_threshold_ms: 100,
        }
    }
}

impl Default for RabbitMQMetricsConfig {
    fn default() -> Self {
        Self {
            // Connection metrics
            connection_metrics_enabled: true,
            connection_latency_buckets: vec![0.1, 0.5, 1.0, 2.0, 5.0],
            tls_metrics_enabled: true,

            // Channel metrics
            channel_metrics_enabled: true,
            channel_latency_buckets: vec![0.05, 0.1, 0.25, 0.5, 1.0],

            // Message metrics
            message_metrics_enabled: true,
            message_size_buckets: vec![1024.0, 4096.0, 16384.0, 65536.0, 262144.0],
            message_processing_buckets: vec![0.01, 0.05, 0.1, 0.5, 1.0],

            // Queue metrics
            queue_metrics_enabled: true,
            queue_size_enabled: true,
            queue_memory_enabled: true,
            queue_consumer_enabled: true,
            queue_growth_rate_enabled: true,

            // Exchange metrics
            exchange_metrics_enabled: true,
            exchange_binding_enabled: true,
            routing_metrics_enabled: true,

            // Consumer metrics
            consumer_metrics_enabled: true,
            consumer_latency_buckets: vec![0.01, 0.05, 0.1, 0.25, 0.5],
            consumer_utilization_enabled: true,
            consumer_lag_enabled: true,
        }
    }
}

impl Default for SurrealDBConfig {
    fn default() -> Self {
        Self {
            connection_name: "prisma_ai_surrealdb".to_string(),
            enable_query_tracing: true,
            enable_transaction_tracing: true,
            enable_document_tracing: true,
            enable_index_tracing: true,
            sampling_rate: 1.0,
            logging: SurrealDBLoggingConfig::default(),
            metrics: SurrealDBMetricsConfig::default(),
            alerts: SurrealDBAlertsConfig::default(),
        }
    }
}

impl Default for SurrealDBLoggingConfig {
    fn default() -> Self {
        Self {
            min_level: Level::INFO,
            stream_name: "surrealdb".to_string(),
            components: SurrealDBLoggingComponents::default(),
            labels: SurrealDBLoggingLabels::default(),
            performance: SurrealDBPerformanceThresholds::default(),
            loki: SurrealDBLokiConfig::default(),
            query: SurrealDBQueryLogging::default(),
            transaction: SurrealDBTransactionLogging::default(),
            document: SurrealDBDocumentLogging::default(),
            index: SurrealDBIndexLogging::default(),
            rag: SurrealDBRagLogging::default(),
        }
    }
}

impl Default for SurrealDBLoggingComponents {
    fn default() -> Self {
        Self {
            query: true,
            transaction: true,
            document: true,
            index: true,
            authentication: true,
            performance: true,
            rag: true,
        }
    }
}

impl Default for SurrealDBLoggingLabels {
    fn default() -> Self {
        Self {
            service: "surrealdb".to_string(),
            environment: "development".to_string(),
        }
    }
}

impl Default for SurrealDBPerformanceThresholds {
    fn default() -> Self {
        Self {
            query_warning_threshold_ms: 500,
            transaction_warning_threshold_ms: 1000,
            index_warning_threshold_ms: 200,
            embedding_warning_threshold_ms: 300,
            vector_search_warning_threshold_ms: 100,
            document_processing_warning_threshold_ms: 200,
        }
    }
}

impl Default for SurrealDBMetricsConfig {
    fn default() -> Self {
        Self {
            // Basic metrics
            basic_metrics_enabled: true,
            operation_latency_buckets: vec![0.01, 0.05, 0.1, 0.25, 0.5],

            // Query metrics
            query_metrics_enabled: true,
            query_latency_buckets: vec![0.01, 0.05, 0.1, 0.25, 0.5],
            query_complexity_enabled: true,
            query_cache_enabled: true,
            query_cache_buckets: vec![0.01, 0.05, 0.1, 0.25, 0.5],

            // Storage metrics
            storage_metrics_enabled: true,
            storage_space_enabled: true,
            storage_buckets: vec![1024.0, 4096.0, 16384.0, 65536.0, 262144.0],

            // Memory metrics
            memory_metrics_enabled: true,
            memory_pressure_enabled: true,
            gc_metrics_enabled: true,
            memory_buckets: vec![1024.0, 4096.0, 16384.0, 65536.0, 262144.0],

            // Transaction metrics
            transaction_metrics_enabled: true,
            transaction_latency_buckets: vec![0.01, 0.05, 0.1, 0.25, 0.5],
            rollback_metrics_enabled: true,
            lock_contention_enabled: true,
            lock_buckets: vec![0.01, 0.05, 0.1, 0.25, 0.5],

            // RAG metrics
            rag_metrics_enabled: true,
            rag_latency_buckets: vec![0.1, 0.5, 1.0, 2.0, 5.0],
            embedding_metrics_enabled: true,
            vector_search_enabled: true,
            document_processing_enabled: true,
            embedding_latency_buckets: vec![0.1, 0.5, 1.0, 2.0, 5.0],
            vector_search_buckets: vec![0.01, 0.05, 0.1, 0.25, 0.5],
            document_processing_buckets: vec![0.1, 0.5, 1.0, 2.0, 5.0],

            // Document metrics
            document_metrics_enabled: true,
            document_count_enabled: true,

            // Index metrics
            index_metrics_enabled: true,
            index_size_enabled: true,
            index_operation_buckets: vec![0.01, 0.05, 0.1, 0.25, 0.5],
        }
    }
}

impl Default for SurrealDBLokiConfig {
    fn default() -> Self {
        let mut labels = HashMap::new();
        labels.insert("app".to_string(), "prisma_ai".to_string());
        labels.insert("component".to_string(), "surrealdb".to_string());
        
        Self {
            endpoint: "http://localhost:3100/loki/api/v1/push".to_string(),
            batch_size: 1000,
            batch_wait_ms: 1000,
            retention_days: 7,
            labels,
        }
    }
}

impl Default for SurrealDBQueryLogging {
    fn default() -> Self {
        Self {
            log_slow_queries: true,
            slow_query_threshold_ms: 500,
            log_query_plans: true,
            log_query_cache_events: true,
        }
    }
}

impl Default for SurrealDBTransactionLogging {
    fn default() -> Self {
        Self {
            log_begin_commit: true,
            log_rollbacks: true,
            log_deadlocks: true,
            log_lock_wait_time: true,
        }
    }
}

impl Default for SurrealDBDocumentLogging {
    fn default() -> Self {
        Self {
            log_mutations: true,
            log_batch_operations: true,
            log_validation_errors: true,
        }
    }
}

impl Default for SurrealDBIndexLogging {
    fn default() -> Self {
        Self {
            log_builds: true,
            log_updates: true,
            log_analysis: true,
        }
    }
}

impl Default for SurrealDBRagLogging {
    fn default() -> Self {
        Self {
            log_embeddings: true,
            log_vector_searches: true,
            log_document_processing: true,
        }
    }
}

impl Default for RabbitMQDashboardsConfig {
    fn default() -> Self {
        Self {
            overview_refresh: "10s".to_string(),
            overview_tags: vec!["rabbitmq".to_string(), "overview".to_string()],
            connection_refresh: "5s".to_string(),
            connection_tags: vec!["rabbitmq".to_string(), "connections".to_string()],
            performance_refresh: "10s".to_string(),
            performance_tags: vec!["rabbitmq".to_string(), "performance".to_string()],
            queue_exchange_refresh: "5s".to_string(),
            queue_exchange_tags: vec!["rabbitmq".to_string(), "queues".to_string(), "exchanges".to_string()],
            error_refresh: "10s".to_string(),
            error_tags: vec!["rabbitmq".to_string(), "errors".to_string()],
        }
    }
}

impl Default for SurrealDBDashboardsConfig {
    fn default() -> Self {
        Self {
            overview_refresh: "10s".to_string(),
            overview_tags: vec!["surrealdb".to_string(), "overview".to_string()],
            performance_refresh: "5s".to_string(),
            performance_tags: vec!["surrealdb".to_string(), "performance".to_string()],
            query_refresh: "5s".to_string(),
            query_tags: vec!["surrealdb".to_string(), "queries".to_string()],
            storage_refresh: "10s".to_string(),
            storage_tags: vec!["surrealdb".to_string(), "storage".to_string()],
            rag_refresh: "10s".to_string(),
            rag_tags: vec!["surrealdb".to_string(), "rag".to_string(), "vector".to_string()],
            error_refresh: "5s".to_string(),
            error_tags: vec!["surrealdb".to_string(), "errors".to_string()],
        }
    }
}

impl Default for SurrealDBAlertsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string()],
            alert_on_slow_queries: true,
            alert_on_transaction_issues: true,
            alert_on_authentication_issues: true,
            alert_on_performance_warnings: true,
            thresholds: SurrealDBAlertThresholds::default(),
        }
    }
}

impl Default for SurrealDBAlertThresholds {
    fn default() -> Self {
        Self {
            slow_query_ms: 1000,
            transaction_timeout_ms: 5000,
            connection_timeout_ms: 3000,
            memory_usage_percentage: 90,
            storage_usage_percentage: 85,
        }
    }
}

impl Default for LLMConfig {
    fn default() -> Self {
        Self {
            enable_inference_tracing: true,
            enable_lora_tracing: true,
            enable_embedding_tracing: true,
            sampling_rate: 1.0,
            tracing: LLMTracingConfig::default(),
            metrics: LLMMetricsConfig::default(),
            logging: LLMLoggingConfig::default(),
            dashboards: LLMDashboardConfig::default(),
            alerts: LLMAlertConfig::default(),  // Add this line
        }
    }
}

impl Default for LLMTracingConfig {
    fn default() -> Self {
        Self {
            model_loading: true,
            inference: true,
            tokenization: true,
            embedding: true,
            lora: true,
            attributes: LLMTracingAttributes::default(),
        }
    }
}

impl Default for LLMMetricsConfig {
    fn default() -> Self {
        Self {
            model_performance_enabled: true,
            resource_usage_enabled: true,
            lora_metrics_enabled: true,
            embedding_metrics_enabled: true,
            inference_latency_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0],
            batch_size_buckets: vec![1.0, 4.0, 8.0, 16.0, 32.0, 64.0],
            memory_usage_buckets: vec![1024.0, 4096.0, 16384.0, 65536.0, 262144.0],
            tokens_per_second_buckets: vec![1.0, 10.0, 50.0, 100.0, 500.0, 1000.0],
        }
    }
}

impl Default for LLMLoggingConfig {
    fn default() -> Self {
        Self {
            min_level: "INFO".to_string(),
            stream_name: "llm".to_string(),
            components: LLMLoggingComponents::default(),
            labels: LLMLoggingLabels::default(),
            performance: LLMLoggingPerformance::default(),
        }
    }
}

impl Default for LLMLoggingComponents {
    fn default() -> Self {
        Self {
            inference: true,
            lora: true,
            embedding: true,
            tokenization: true,
            performance: true,
            resources: true,
        }
    }
}

impl Default for LLMLoggingLabels {
    fn default() -> Self {
        Self {
            service: "llm".to_string(),
            environment: "development".to_string(),
        }
    }
}

impl Default for LLMLoggingPerformance {
    fn default() -> Self {
        Self {
            inference_warning_threshold_ms: 1000,
            lora_warning_threshold_ms: 500,
            embedding_warning_threshold_ms: 200,
            tokenization_warning_threshold_ms: 100,
        }
    }
}

impl Default for LLMDashboardConfig {
    fn default() -> Self {
        Self {
            overview_refresh: "10s".to_string(),
            overview_tags: vec!["llm".to_string(), "overview".to_string()],
            model_performance_refresh: "5s".to_string(),
            model_performance_tags: vec!["llm".to_string(), "performance".to_string(), "inference".to_string()],
            resource_usage_refresh: "10s".to_string(),
            resource_usage_tags: vec!["llm".to_string(), "resources".to_string(), "utilization".to_string()],
            lora_refresh: "5s".to_string(),
            lora_tags: vec!["llm".to_string(), "lora".to_string(), "adapters".to_string()],
            embedding_refresh: "10s".to_string(),
            embedding_tags: vec!["llm".to_string(), "embeddings".to_string(), "vectors".to_string()],
        }
    }
}

impl Default for LLMAlertConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string()],
            performance: LLMPerformanceAlerts::default(),
            errors: LLMErrorAlerts::default(),
            resources: LLMResourceAlerts::default(),
            quality: LLMQualityAlerts::default(),
        }
    }
}

impl Default for LLMPerformanceAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            high_latency_threshold_ms: 1000,
            low_throughput_threshold_tokens: 100,
            resource_exhaustion_percentage: 90,
        }
    }
}

impl Default for LLMErrorAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            alert_on_model_loading: true,
            alert_on_inference_failures: true,
            alert_on_out_of_memory: true,
            max_consecutive_failures: 3,
        }
    }
}

impl Default for LLMResourceAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            gpu_memory_threshold_percentage: 85,
            cpu_utilization_threshold_percentage: 80,
            system_memory_threshold_percentage: 90,
            monitoring_interval_seconds: 30,
        }
    }
}

impl Default for LLMQualityAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            token_error_threshold_percentage: 5.0,
            embedding_quality_threshold: 0.8,
            context_window_usage_percentage: 90,
            max_invalid_tokens_per_batch: 10,
        }
    }
}

impl Default for SystemErrorTracingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            include_context: true,
            include_stack_trace: true,
            propagate_correlation_id: true,
            sampling_rate: 1.0,
            error_group_window_seconds: 300,
            circuit_breaker_events: true,
            retry_events: true,
            timeout_events: true,
        }
    }
}

impl Default for SystemErrorDashboardsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            refresh_interval: "10s".to_string(),
            error_dashboard_tags: vec!["errors".to_string(), "system".to_string(), "monitoring".to_string()],
            circuit_breakers_dashboard_tags: vec!["resilience".to_string(), "circuit-breakers".to_string(), "system".to_string()],
            retries_dashboard_tags: vec!["resilience".to_string(), "retries".to_string(), "system".to_string()],
            error_distribution_dashboard_tags: vec!["errors".to_string(), "distribution".to_string(), "system".to_string()],
            error_timeline_dashboard_tags: vec!["errors".to_string(), "timeline".to_string(), "system".to_string()],
            error_severity_distribution_refresh: "30s".to_string(),
            error_source_distribution_refresh: "30s".to_string(),
            error_rate_timeline_refresh: "10s".to_string(),
            circuit_breaker_status_refresh: "5s".to_string(),
            retry_count_refresh: "15s".to_string(),
            service_health_refresh: "5s".to_string(),
            error_counts_by_service_refresh: "15s".to_string(),
            error_counts_by_type_refresh: "20s".to_string(),
            top_errors_refresh: "30s".to_string(),
        }
    }
}

impl Default for TelemetryRabbitMQConfig {
    fn default() -> Self {
        Self {
            enable_connection_metrics: true,
            enable_channel_metrics: true,
            enable_message_metrics: true,
            sampling_rate: 1.0,
        }
    }
}
