mod rabbitmq;
pub mod llm;
pub mod storage;
mod system;

pub use rabbitmq::*;
pub use llm::*;
pub use storage::*;
pub use system::*;

use prometheus::{Registry, TextEncoder, Opts, GaugeVec, IntCounterVec, HistogramVec};
use crate::telemetry::core::{TelemetryError, TelemetryResult, TelemetryConfig};
use lazy_static::lazy_static;
use std::sync::{Arc, RwLock};
use log::{info, warn};

lazy_static! {
    static ref REGISTRY: Registry = Registry::new();

    // Queue metrics with labels
    pub(crate) static ref QUEUE_SIZE: GaugeVec = GaugeVec::new(
        Opts::new("rabbitmq_queue_size", "Current number of messages in queue"),
        &["queue"]
    ).unwrap();

    pub(crate) static ref QUEUE_CONSUMERS: GaugeVec = GaugeVec::new(
        Opts::new("rabbitmq_queue_consumers", "Current number of consumers per queue"),
        &["queue"]
    ).unwrap();

    pub(crate) static ref QUEUE_MEMORY: GaugeVec = GaugeVec::new(
        Opts::new("rabbitmq_queue_memory_bytes", "Current memory usage by queue in bytes"),
        &["queue"]
    ).unwrap();

    // Agent Metrics
    pub static ref AGENT_OPERATIONS: IntCounterVec = IntCounterVec::new( // Make public
        Opts::new("agent_operations_total", "Total number of agent operations"),
        &["agent_id", "operation_type"]
    ).unwrap();

    pub static ref AGENT_PROCESSING_TIME: HistogramVec = HistogramVec::new( // Make public
        prometheus::HistogramOpts::new("agent_processing_duration_seconds", "Time taken for agent processing operations"),
        &["agent_id", "operation_type"]
    ).unwrap();

    pub static ref AGENT_ERRORS: IntCounterVec = IntCounterVec::new( // Make public
        Opts::new("agent_errors_total", "Total number of agent errors"),
        &["agent_id", "error_type"]
    ).unwrap();
}

pub struct MetricsCollector {
    encoder: TextEncoder,
    config: TelemetryConfig,
    last_reload: std::time::SystemTime,
}

impl MetricsCollector {
    pub fn new(config: TelemetryConfig) -> TelemetryResult<Self> {
        // Register metrics based on configuration (only if RabbitMQ is configured)
        if let Some(ref rabbitmq_config) = config.rabbitmq {
            if rabbitmq_config.metrics.queue_metrics_enabled {
                REGISTRY.register(Box::new(QUEUE_SIZE.clone())).unwrap();
                REGISTRY.register(Box::new(QUEUE_CONSUMERS.clone())).unwrap();
                REGISTRY.register(Box::new(QUEUE_MEMORY.clone())).unwrap();
            }
        }

        // Register agent metrics
        REGISTRY.register(Box::new(AGENT_OPERATIONS.clone())).unwrap();
        REGISTRY.register(Box::new(AGENT_PROCESSING_TIME.clone())).unwrap();
        REGISTRY.register(Box::new(AGENT_ERRORS.clone())).unwrap();

        Ok(Self {
            encoder: TextEncoder::new(),
            config,
            last_reload: std::time::SystemTime::now(),
        })
    }

    pub fn reload_config(&mut self, new_config: TelemetryConfig) -> TelemetryResult<()> {
        // Handle RabbitMQ metrics changes (only if both configs have RabbitMQ)
        match (&new_config.rabbitmq, &self.config.rabbitmq) {
            (Some(new_rabbitmq), Some(old_rabbitmq)) => {
                // Unregister existing metrics if they're disabled in new config
                if !new_rabbitmq.metrics.queue_metrics_enabled && old_rabbitmq.metrics.queue_metrics_enabled {
                    REGISTRY.unregister(Box::new(QUEUE_SIZE.clone())).ok();
                    REGISTRY.unregister(Box::new(QUEUE_CONSUMERS.clone())).ok();
                    REGISTRY.unregister(Box::new(QUEUE_MEMORY.clone())).ok();
                }

                // Register new metrics if they're enabled
                if new_rabbitmq.metrics.queue_metrics_enabled && !old_rabbitmq.metrics.queue_metrics_enabled {
                    REGISTRY.register(Box::new(QUEUE_SIZE.clone())).unwrap();
                    REGISTRY.register(Box::new(QUEUE_CONSUMERS.clone())).unwrap();
                    REGISTRY.register(Box::new(QUEUE_MEMORY.clone())).unwrap();
                }
            }
            (Some(new_rabbitmq), None) => {
                // RabbitMQ was added - register metrics if enabled
                if new_rabbitmq.metrics.queue_metrics_enabled {
                    REGISTRY.register(Box::new(QUEUE_SIZE.clone())).unwrap();
                    REGISTRY.register(Box::new(QUEUE_CONSUMERS.clone())).unwrap();
                    REGISTRY.register(Box::new(QUEUE_MEMORY.clone())).unwrap();
                }
            }
            (None, Some(_)) => {
                // RabbitMQ was removed - unregister all metrics
                REGISTRY.unregister(Box::new(QUEUE_SIZE.clone())).ok();
                REGISTRY.unregister(Box::new(QUEUE_CONSUMERS.clone())).ok();
                REGISTRY.unregister(Box::new(QUEUE_MEMORY.clone())).ok();
            }
            (None, None) => {
                // No RabbitMQ in either config - nothing to do
            }
        }

        self.config = new_config;
        self.last_reload = std::time::SystemTime::now();
        Ok(())
    }

    pub fn start(&self) -> TelemetryResult<()> {
        info!("Starting metrics collector...");
        Ok(())
    }

    pub fn stop(&self) -> TelemetryResult<()> {
        info!("Stopping metrics collector...");
        Ok(())
    }

    pub fn collect(&self) -> TelemetryResult<String> {
        let metric_families = REGISTRY.gather();
        self.encoder
            .encode_to_string(&metric_families)
            .map_err(|e| TelemetryError::MetricsError(e.to_string()))
    }

    pub fn is_metric_enabled(&self, metric_type: &str) -> bool {
        // Return false if RabbitMQ is not configured
        let rabbitmq_config = match &self.config.rabbitmq {
            Some(config) => config,
            None => return false,
        };

        match metric_type {
            "queue" => rabbitmq_config.metrics.queue_metrics_enabled,
            "connection" => rabbitmq_config.metrics.connection_metrics_enabled,
            "channel" => rabbitmq_config.metrics.channel_metrics_enabled,
            "message" => rabbitmq_config.metrics.message_metrics_enabled,
            "exchange" => rabbitmq_config.metrics.exchange_metrics_enabled,
            "consumer" => rabbitmq_config.metrics.consumer_metrics_enabled,
            _ => false,
        }
    }
}

// Add a new type for managing metric collectors
pub struct MetricsManager {
    collectors: Vec<Box<dyn MetricCollector>>,
    config: Arc<RwLock<TelemetryConfig>>,
}

impl MetricsManager {
    pub fn new(config: TelemetryConfig) -> TelemetryResult<Self> {
        Ok(Self {
            collectors: Vec::new(),
            config: Arc::new(RwLock::new(config)),
        })
    }

    pub fn register_collector(&mut self, collector: Box<dyn MetricCollector>) {
        self.collectors.push(collector);
    }

    pub fn start_all(&self) -> TelemetryResult<()> {
        for collector in &self.collectors {
            collector.start()?;
        }
        Ok(())
    }

    pub fn stop_all(&self) -> TelemetryResult<()> {
        for collector in &self.collectors {
            collector.stop()?;
        }
        Ok(())
    }

    pub fn reload_config(&mut self, new_config: TelemetryConfig) -> TelemetryResult<()> {
        if let Ok(mut config) = self.config.write() {
            *config = new_config.clone();
        }

        for collector in &mut self.collectors {
            collector.reload_config(new_config.clone())?;
        }
        Ok(())
    }
}

// Add trait for metric collectors
pub trait MetricCollector: Send + Sync {
    fn start(&self) -> TelemetryResult<()>;
    fn stop(&self) -> TelemetryResult<()>;
    fn reload_config(&mut self, config: TelemetryConfig) -> TelemetryResult<()>;
    fn collect(&self) -> TelemetryResult<String>;
}

// Implement the trait for MetricsCollector
impl MetricCollector for MetricsCollector {
    fn start(&self) -> TelemetryResult<()> {
        self.start()
    }

    fn stop(&self) -> TelemetryResult<()> {
        self.stop()
    }

    fn reload_config(&mut self, config: TelemetryConfig) -> TelemetryResult<()> {
        self.reload_config(config)
    }

    fn collect(&self) -> TelemetryResult<String> {
        self.collect()
    }
}
