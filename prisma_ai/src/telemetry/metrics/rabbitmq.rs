use lazy_static::lazy_static;
use prometheus::{register_int_counter, register_histogram, register_gauge, IntCounter, Histogram, Gauge, opts, HistogramOpts};
use super::{QUEUE_SIZE, QUEUE_CONSUMERS, QUEUE_MEMORY, MetricCollector};
use crate::telemetry::core::{TelemetryConfig, TelemetryResult};
use std::time::{Instant, Duration};
use std::sync::{Arc, RwLock};
use log::{info, warn};

pub struct RabbitMQMetricsCollector {
    metrics: Arc<RabbitMQMetrics>,
    config: Arc<RwLock<TelemetryConfig>>,
    last_reload: std::time::SystemTime,
}

struct RabbitMQMetrics {
// RabbitMQ Connection Metrics
    pub connection_latency: Option<Histogram>,
    pub tls_handshake_time: Option<Histogram>,
    pub tls_version: Option<Gauge>,
    pub tls_errors: Option<IntCounter>,

    // RabbitMQ Channel Metrics
    pub channel_latency: Option<Histogram>,
    pub channel_errors: Option<IntCounter>,

    // RabbitMQ Message Metrics
    pub message_size: Option<Histogram>,
    pub message_processing_time: Option<Histogram>,

    // RabbitMQ Queue Metrics
    pub queue_growth_rate: Option<Gauge>,
    pub queue_memory_limit: Option<Gauge>,
    pub queue_memory_alarm: Option<Gauge>,

    // RabbitMQ Exchange Metrics
    pub exchange_bindings: Option<Gauge>,
    pub routing_errors: Option<IntCounter>,

    // RabbitMQ Consumer Metrics
    pub consumer_utilization: Option<Gauge>,
    pub consumer_lag: Option<Gauge>,
    pub consumer_processing_time: Option<Histogram>,
}

impl RabbitMQMetricsCollector {
    pub fn new(config: TelemetryConfig) -> TelemetryResult<Self> {
        let metrics = Arc::new(RabbitMQMetrics::new(&config)?);
        
        Ok(Self {
            metrics,
            config: Arc::new(RwLock::new(config)),
            last_reload: std::time::SystemTime::now(),
        })
    }

    pub fn reload_metrics(&mut self, new_config: &TelemetryConfig) -> TelemetryResult<()> {
        // Create new metrics based on new configuration
        let new_metrics = RabbitMQMetrics::new(new_config)?;
        
        // Update the metrics atomically
        self.metrics = Arc::new(new_metrics);
        self.last_reload = std::time::SystemTime::now();
        
        Ok(())
    }
}

impl RabbitMQMetrics {
    fn new(config: &TelemetryConfig) -> TelemetryResult<Self> {
        // Check if RabbitMQ is configured
        let metrics_config = match &config.rabbitmq {
            Some(rabbitmq_config) => &rabbitmq_config.metrics,
            None => {
                // Return a disabled metrics instance if RabbitMQ is not configured
                return Ok(Self {
                    connection_latency: None,
                    tls_handshake_time: None,
                    tls_version: None,
                    tls_errors: None,
                    channel_latency: None,
                    channel_errors: None,
                    message_size: None,
                    message_processing_time: None,
                    queue_growth_rate: None,
                    queue_memory_limit: None,
                    queue_memory_alarm: None,
                    exchange_bindings: None,
                    routing_errors: None,
                    consumer_utilization: None,
                    consumer_lag: None,
                    consumer_processing_time: None,
                });
            }
        };
        
        Ok(Self {
            connection_latency: if metrics_config.connection_metrics_enabled {
                Some(register_histogram!(
                    HistogramOpts::new(
        "rabbitmq_connection_latency_seconds",
        "Time taken to establish RabbitMQ connections"
                    ).buckets(metrics_config.connection_latency_buckets.clone())
                ).unwrap())
            } else {
                None
            },
            tls_handshake_time: if metrics_config.tls_metrics_enabled {
                Some(register_histogram!(
        "rabbitmq_tls_handshake_seconds",
        "Time taken for TLS/SSL handshake"
                ).unwrap())
            } else {
                None
            },
            tls_version: if metrics_config.tls_metrics_enabled {
                Some(register_gauge!(
        "rabbitmq_tls_version",
        "TLS version in use (1.0=1, 1.1=2, 1.2=3, 1.3=4)"
                ).unwrap())
            } else {
                None
            },
            tls_errors: if metrics_config.tls_metrics_enabled {
                Some(register_int_counter!(
        "rabbitmq_tls_errors_total",
        "Total number of TLS/SSL related errors"
                ).unwrap())
            } else {
                None
            },
            channel_latency: if metrics_config.channel_metrics_enabled {
                Some(register_histogram!(
                    "rabbitmq_channel_latency_seconds",
                    "Time taken to establish RabbitMQ channels"
                ).unwrap())
            } else {
                None
            },
            channel_errors: if metrics_config.channel_metrics_enabled {
                Some(register_int_counter!(
        "rabbitmq_channel_errors_total",
        "Total number of RabbitMQ channel errors"
                ).unwrap())
            } else {
                None
            },
            message_size: if metrics_config.message_metrics_enabled {
                Some(register_histogram!(
                    "rabbitmq_message_size_bytes",
                    "Size of messages in bytes"
                ).unwrap())
            } else {
                None
            },
            message_processing_time: if metrics_config.message_metrics_enabled {
                Some(register_histogram!(
        "rabbitmq_message_processing_seconds",
        "Time taken to process RabbitMQ messages"
                ).unwrap())
            } else {
                None
            },
            queue_growth_rate: if metrics_config.queue_metrics_enabled {
                Some(register_gauge!(
        "rabbitmq_queue_growth_rate",
        "Rate of change in queue size (messages/second)"
                ).unwrap())
            } else {
                None
            },
            queue_memory_limit: if metrics_config.queue_metrics_enabled {
                Some(register_gauge!(
        "rabbitmq_queue_memory_limit_bytes",
        "Memory limit configured for queue in bytes"
                ).unwrap())
            } else {
                None
            },
            queue_memory_alarm: if metrics_config.queue_metrics_enabled {
                Some(register_gauge!(
        "rabbitmq_queue_memory_alarm",
        "Memory alarm status for queue (0=OK, 1=Warning, 2=Critical)"
                ).unwrap())
            } else {
                None
            },
            exchange_bindings: if metrics_config.exchange_metrics_enabled {
                Some(register_gauge!(
        "rabbitmq_exchange_bindings",
        "Current number of exchange bindings"
                ).unwrap())
            } else {
                None
            },
            routing_errors: if metrics_config.exchange_metrics_enabled {
                Some(register_int_counter!(
        "rabbitmq_routing_errors_total",
        "Total number of message routing errors"
                ).unwrap())
            } else {
                None
            },
            consumer_utilization: if metrics_config.consumer_metrics_enabled {
                Some(register_gauge!(
        "rabbitmq_consumer_utilization",
        "Consumer utilization percentage"
                ).unwrap())
            } else {
                None
            },
            consumer_lag: if metrics_config.consumer_metrics_enabled {
                Some(register_gauge!(
        "rabbitmq_consumer_lag",
        "Number of messages a consumer is behind"
                ).unwrap())
            } else {
                None
            },
            consumer_processing_time: if metrics_config.consumer_metrics_enabled {
                Some(register_histogram!(
        HistogramOpts::new(
            "rabbitmq_consumer_processing_seconds",
            "Time taken by consumer to process messages"
                    ).buckets(metrics_config.consumer_latency_buckets.clone())
                ).unwrap())
            } else {
                None
            },
        })
    }
}

impl MetricCollector for RabbitMQMetricsCollector {
    fn start(&self) -> TelemetryResult<()> {
        info!("Starting RabbitMQ metrics collector...");
        Ok(())
    }

    fn stop(&self) -> TelemetryResult<()> {
        info!("Stopping RabbitMQ metrics collector...");
        Ok(())
    }

    fn reload_config(&mut self, config: TelemetryConfig) -> TelemetryResult<()> {
        if let Ok(mut current_config) = self.config.write() {
            *current_config = config.clone();
        }
        self.reload_metrics(&config)
    }

    fn collect(&self) -> TelemetryResult<String> {
        // Implement metrics collection
        Ok("RabbitMQ metrics collected".to_string())
    }
}

// Helper functions for recording metrics
impl RabbitMQMetricsCollector {
    pub fn record_connection_latency(&self, duration: Duration) {
        if let Some(ref histogram) = self.metrics.connection_latency {
            histogram.observe(duration.as_secs_f64());
        }
}

    pub fn record_tls_handshake_time(&self, duration: Duration) {
        if let Some(ref histogram) = self.metrics.tls_handshake_time {
            histogram.observe(duration.as_secs_f64());
        }
}

    pub fn record_tls_version(&self, version: f64) {
        if let Some(ref gauge) = self.metrics.tls_version {
            gauge.set(version);
        }
}

    pub fn record_tls_error(&self) {
        if let Some(ref counter) = self.metrics.tls_errors {
            counter.inc();
        }
}

    pub fn record_channel_latency(&self, duration: Duration) {
        if let Some(ref histogram) = self.metrics.channel_latency {
            histogram.observe(duration.as_secs_f64());
        }
}

    pub fn record_channel_error(&self) {
        if let Some(ref counter) = self.metrics.channel_errors {
            counter.inc();
        }
}

    pub fn record_message_size(&self, size: usize) {
        if let Some(ref histogram) = self.metrics.message_size {
            histogram.observe(size as f64);
        }
}

    pub fn record_message_processing_time(&self, duration: f64) {
        if let Some(ref histogram) = self.metrics.message_processing_time {
            histogram.observe(duration);
        }
}

    pub fn record_queue_growth_rate(&self, rate: f64) {
        if let Some(ref gauge) = self.metrics.queue_growth_rate {
            gauge.set(rate);
        }
}

    pub fn record_queue_memory_limit(&self, bytes: i64) {
        if let Some(ref gauge) = self.metrics.queue_memory_limit {
            gauge.set(bytes as f64);
        }
}

    pub fn record_queue_memory_alarm(&self, status: i64) {
        if let Some(ref gauge) = self.metrics.queue_memory_alarm {
            gauge.set(status as f64);
        }
}

    pub fn record_exchange_type(&self, count: i64) {
        if let Some(ref gauge) = self.metrics.exchange_bindings {
            gauge.set(count as f64);
        }
}

    pub fn record_consumer_utilization(&self, utilization: f64) {
        if let Some(ref gauge) = self.metrics.consumer_utilization {
            gauge.set(utilization);
        }
}

    pub fn record_consumer_lag(&self, lag: i64) {
        if let Some(ref gauge) = self.metrics.consumer_lag {
            gauge.set(lag as f64);
        }
}

    pub fn record_consumer_processing_time(&self, duration: Duration) {
        if let Some(ref histogram) = self.metrics.consumer_processing_time {
            histogram.observe(duration.as_secs_f64());
        }
    }
}

// Re-export the existing helper functions but make them use the collector
pub fn record_message_published(collector: &RabbitMQMetricsCollector) {
    // Implementation using collector
}

pub fn record_message_consumed(collector: &RabbitMQMetricsCollector) {
    // Implementation using collector
}

pub fn record_routing_error(collector: &RabbitMQMetricsCollector) {
    // Implementation using collector
}
