use crate::telemetry::alerts::types::{<PERSON>ert, AlertDestination, AlertLevel};
use crate::telemetry::core::config;
use crate::telemetry::alerts::types;
use async_trait::async_trait;
use chrono::Utc;
use lapin::{
    options::*, types::FieldTable, BasicProperties, Connection, ConnectionProperties, ExchangeKind,
};
use reqwest::Client;
use serde_json::json;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{error, info, warn};
use uuid;
// Use the tokio-executor-trait library which provides a compatible executor for lapin
use tokio_executor_trait::Tokio;

pub struct RabbitMQAlerts {
    config: Arc<config::Settings>,
    destinations: Vec<AlertDestination>,
    http_client: Client,
    rabbitmq_conn: Arc<Mutex<Option<Connection>>>,
}

impl RabbitMQAlerts {
    // Refactored to use config struct instead of environment variables
    pub async fn new(config: Arc<config::Settings>) -> anyhow::Result<Self> {
        let mut destinations = Vec::new();
        let api_keys = &config.global.api_keys; // Shortcut

        // Configure Slack alerts if enabled in config
        if api_keys.slack_enabled && !api_keys.slack_token.is_empty() {
            info!("Configuring Slack alert destination for channel: {}", api_keys.slack_channel);
            destinations.push(AlertDestination::Slack(types::SlackDestination {
                token: api_keys.slack_token.clone(),
                channel: api_keys.slack_channel.clone(),
            }));
        } else {
             info!("Slack alerts disabled or token/channel not configured.");
        }

        // Configure Discord alerts if enabled in config
        if api_keys.discord_enabled && !api_keys.discord_webhook.is_empty() {
             info!("Configuring Discord alert destination.");
            destinations.push(AlertDestination::Discord(types::DiscordDestination {
                webhook_url: api_keys.discord_webhook.clone(),
            }));
        } else {
            info!("Discord alerts disabled or webhook not configured.");
        }

        // Configure Email alerts if enabled in config
        if api_keys.gmail_enabled && !api_keys.gmail_email.is_empty() {
             info!("Configuring Email alert destination for: {}", api_keys.gmail_email);
            destinations.push(AlertDestination::Email(types::EmailDestination {
                email: api_keys.gmail_email.clone(),
                app_password: api_keys.gmail_app_password.clone(), // Use configured password
            }));
        } else {
             info!("Gmail alerts disabled or email not configured.");
        }

        // Note: UI destination configuration removed as it wasn't in global config.
        // It might need separate handling if still required.

        Ok(Self {
            config,
            destinations,
            http_client: Client::new(),
            rabbitmq_conn: Arc::new(Mutex::new(None)),
        })
    }

    pub async fn connect(&self) -> anyhow::Result<()> {
        // Check if RabbitMQ is configured for telemetry
        let rabbitmq_config = match &self.config.telemetry.rabbitmq {
            Some(config) => config,
            None => {
                info!("RabbitMQ not configured for telemetry alerts - skipping connection");
                return Ok(()); // Skip RabbitMQ connection for basic telemetry
            }
        };

        let addr = format!(
            "amqp://{}:{}@{}:{}/{}",
            rabbitmq_config.username,
            rabbitmq_config.password,
            rabbitmq_config.host,
            rabbitmq_config.port,
            rabbitmq_config.vhost.trim_start_matches('/')
        );

        // Fix: Use Tokio from tokio-executor-trait instead of Handle::current()
        let conn_properties = ConnectionProperties::default()
            .with_connection_name(rabbitmq_config.connection_name.clone().into())
            .with_executor(Tokio::current());

        // Connect to RabbitMQ
        let conn = Connection::connect(&addr, conn_properties).await?;

        let mut conn_guard = self.rabbitmq_conn.lock().await;
        *conn_guard = Some(conn);
        Ok(())
    }

    pub async fn setup_ui_alerts(&self) -> anyhow::Result<()> {
        if let Some(conn) = &*self.rabbitmq_conn.lock().await {
            let channel = conn.create_channel().await?;
            
            // Define alert queue and exchange settings
            let queue_name = "alerts.ui";
            let exchange_name = "alerts.topic";
            let routing_key = "alerts.#";
            let prefetch_count = 10;
            let durable = true;
            let exclusive = false;
            let auto_delete = false;

            // Set channel prefetch count
            channel
                .basic_qos(prefetch_count, BasicQosOptions::default())
                .await?;

            // Declare exchange
            channel
                .exchange_declare(
                    exchange_name,
                    ExchangeKind::Topic,
                    ExchangeDeclareOptions {
                        durable,
                        auto_delete,
                        ..Default::default()
                    },
                    FieldTable::default(),
                )
                .await?;

            // Declare queue
            channel
                .queue_declare(
                    queue_name,
                    QueueDeclareOptions {
                        durable,
                        exclusive,
                        auto_delete,
                        ..Default::default()
                    },
                    FieldTable::default(),
                )
                .await?;

            // Bind queue to exchange
            channel
                .queue_bind(
                    queue_name,
                    exchange_name,
                    routing_key,
                    QueueBindOptions::default(),
                    FieldTable::default(),
                )
                .await?;

            info!(
                "UI alerts setup complete: exchange={}, queue={}, routing_key={}",
                exchange_name, queue_name, routing_key
            );
        }
        Ok(())
    }

    pub async fn send_alert(&self, level: AlertLevel, message: &str, metadata: Option<HashMap<String, String>>) -> anyhow::Result<()> {
        // Define enabled log levels
        let enabled_levels = vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string()];
        
        // Check if this alert level should be processed based on enabled levels
        if !Self::should_send_alert(&level, &enabled_levels) {
            return Ok(());
        }

        let alert = Alert {
            level: level.clone(),
            module: "rabbitmq".to_string(),
            message: message.to_string(),
            timestamp: Utc::now(),
            metadata: metadata.unwrap_or_default(),
        };

        for destination in &self.destinations {
            match destination {
                AlertDestination::UI(ui) => {
                    // Always send to UI if destination is configured
                    self.send_ui_alert(&alert, ui).await?;
                }
                AlertDestination::Slack(slack) => {
                    // Always send to Slack if destination is configured
                    self.send_slack_alert(&alert, slack).await?;
                }
                AlertDestination::Discord(discord) => {
                    // Always send to Discord if destination is configured
                    self.send_discord_alert(&alert, discord).await?;
                }
                AlertDestination::Email(email) => {
                    // Always send to Email if destination is configured
                    self.send_email_alert(&alert, email).await?;
                }
            }
        }
        Ok(())
    }

    // Helper function to determine if an alert should be sent based on log levels
    fn should_send_alert(alert_level: &AlertLevel, selected_levels: &[String]) -> bool {
        selected_levels.contains(&alert_level.to_string())
    }

    // New method to check RabbitMQ thresholds and generate alerts
    pub async fn check_thresholds(&self) -> anyhow::Result<()> {
        // Define thresholds - in a real app these would come from config
        let connection_timeout_ms = 5000;
        let queue_full_percentage = 80.0;
        
        // Check connection timeout
        let alert_on_connection_issues = true;
        if alert_on_connection_issues {
            let start = std::time::Instant::now();
            if let Err(e) = self.check_connection().await {
                if start.elapsed().as_millis() as u64 > connection_timeout_ms {
                    self.send_alert(
                        AlertLevel::WARN,
                        &format!("Connection timeout exceeded: {:?}", e),
                        None,
                    ).await?;
                }
            }
        }

        // Check queue fullness if enabled
        let alert_on_queue_issues = true;
        if alert_on_queue_issues {
            if let Some(conn) = &*self.rabbitmq_conn.lock().await {
                let channel = conn.create_channel().await?;
                
                // Get queue info - use a predefined queue name
                let queue_name = "alerts.ui";
                let queue_info = channel
                    .queue_declare(
                        queue_name,
                        QueueDeclareOptions::default(),
                        FieldTable::default(),
                    )
                    .await?;

                // Use methods instead of accessing fields directly
                let message_count = queue_info.message_count();
                let consumer_count = queue_info.consumer_count();
                
                // Avoid division by zero
                if consumer_count > 0 {
                    let usage_percent = (message_count as f64 / consumer_count as f64) * 100.0;
                    if usage_percent > queue_full_percentage {
                        self.send_alert(
                            AlertLevel::WARN,
                            &format!("Queue usage at {}% (threshold: {}%)", 
                                usage_percent, 
                                queue_full_percentage
                            ),
                            None,
                        ).await?;
                    }
                }
            }
        }

        Ok(())
    }

    async fn check_connection(&self) -> anyhow::Result<()> {
        if let Some(conn) = &*self.rabbitmq_conn.lock().await {
            let channel = conn.create_channel().await?;
            channel.close(0, "Health check").await?;
            Ok(())
        } else {
            Err(anyhow::anyhow!("No connection available"))
        }
    }

    async fn send_ui_alert(&self, alert: &Alert, ui: &types::UIDestination) -> anyhow::Result<()> {
        if let Some(conn) = &*self.rabbitmq_conn.lock().await {
            let channel = conn.create_channel().await?;
            let payload = serde_json::to_vec(&alert)?;

            // Create routing key based on alert level and module
            let routing_key = format!("alerts.{}.{}", alert.level.to_string().to_lowercase(), alert.module);

            channel
                .basic_publish(
                    &ui.exchange_name,
                    &routing_key,
                    BasicPublishOptions::default(),
                    &payload,
                    BasicProperties::default()
                        .with_content_type("application/json".into())
                        .with_delivery_mode(2) // persistent
                        .with_timestamp(alert.timestamp.timestamp() as u64)
                        .with_message_id(uuid::Uuid::new_v4().to_string().into()),
                )
                .await?;

            info!(
                "Alert sent to UI: level={}, module={}, routing_key={}",
                alert.level, alert.module, routing_key
            );
        }
        Ok(())
    }

    async fn send_slack_alert(&self, alert: &Alert, slack: &types::SlackDestination) -> anyhow::Result<()> {
        let text = format!(
            ":bell: *{}* alert from RabbitMQ:\n>{}",
            alert.level, alert.message
        );

        let payload = json!({
            "channel": slack.channel,
            "text": text,
        });

        self.http_client
            .post("https://slack.com/api/chat.postMessage")
            .header("Authorization", format!("Bearer {}", slack.token))
            .json(&payload)
            .send()
            .await?;

        Ok(())
    }

    async fn send_discord_alert(&self, alert: &Alert, discord: &types::DiscordDestination) -> anyhow::Result<()> {
        let content = format!(
            "**{}** alert from RabbitMQ:\n> {}",
            alert.level, alert.message
        );

        let payload = json!({
            "content": content,
        });

        self.http_client
            .post(&discord.webhook_url)
            .json(&payload)
            .send()
            .await?;

        Ok(())
    }

    async fn send_email_alert(&self, alert: &Alert, email: &types::EmailDestination) -> anyhow::Result<()> {
        // Note: Implement email sending using lettre or similar
        // This is a placeholder for the email implementation
        info!("Email alert would be sent to {} with message: {}", email.email, alert.message);
        Ok(())
    }
}

// Define a new trait for RabbitMQ alerts telemetry functionality
#[async_trait]
pub trait RabbitMQAlertsTelemetry {
    async fn start(&self) -> anyhow::Result<()>;
    async fn stop(&self) -> anyhow::Result<()>;
}

#[async_trait]
impl RabbitMQAlertsTelemetry for RabbitMQAlerts {
    async fn start(&self) -> anyhow::Result<()> {
        self.connect().await?;
        self.setup_ui_alerts().await?;
        Ok(())
    }

    async fn stop(&self) -> anyhow::Result<()> {
        if let Some(conn) = &*self.rabbitmq_conn.lock().await {
            conn.close(0, "Shutting down").await?;
        }
        Ok(())
    }
}
