use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WebhookServerConfig {
    pub enabled: bool,
    pub host: String,
    pub port: u16,
    pub grafana: GrafanaWebhookConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GrafanaWebhookConfig {
    pub enabled: bool,
    pub endpoint: String,
    pub service_token: String,
}

impl GrafanaWebhookConfig {
    pub fn from_global_config(config: &crate::config::Settings) -> Self {
        Self {
            enabled: config.global.api_keys.prisma_ai_enabled 
                    && config.telemetry.endpoints.grafana_enabled,
            endpoint: config.telemetry.endpoints.grafana.clone(),
            service_token: config.telemetry.grafana_service_token.clone(),
        }
    }
}

impl Default for WebhookServerConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            host: "0.0.0.0".to_string(),
            port: 8080,
            grafana: GrafanaWebhookConfig {
                enabled: false,
                endpoint: "http://localhost:3000".to_string(),
                service_token: String::new(),
            },
        }
    }
}