# settings.toml - Corrected Structure

[global]
# Values from temp_config lines 22-25
service_name = "prisma-pipeline"
environment = "development"
service_id = "sa-1-prisma-pipeline"

  [global.endpoints]
  # Values from temp_config lines 10-15
  prometheus_enabled = true
  prometheus = "http://*************:30158"
  grafana_enabled = true
  grafana = "http://*************:30455"
  grafana_version = "11.4.0"  # Latest stable version
  loki_enabled = true
  loki = "http://*************:32278/loki/api/v1/push"
  loki_version = "3.3.0"  # Latest stable version

  [global.api_keys]
  # Values from temp_config lines 47-55
  prisma_ai_enabled = true
  slack_enabled = true
  slack_token = "*********************************************************"
  slack_channel = "C08J945BFD2"
  discord_enabled = true
  discord_webhook = "https://discord.com/api/webhooks/1351394594624897107/49-3vXkwo2eoS_j_Wk9EwWr7vBZL-_pvWk8rvIy850TumXPetQhz8yEZ-7PQCmpei0ki"
  gmail_enabled = true
  gmail_email = "<EMAIL>"
  gmail_app_password = "ufuq nuok mgjs hnam"

  [global.alerts]
  # Values from temp_config lines 63-65
  enabled = true
  default_log_level = "INFO"
  enabled_modules = ["rabbitmq", "llm", "storage"]

    [global.alerts.slack]
    # Values from temp_config lines 68-72
    enabled = true
    log_levels = ["ERROR", "WARN", "INFO", "DEBUG", "TRACE"]
    default_level = "INFO"
    modules = ["rabbitmq", "llm", "storage"]
    message_template = ":bell: *{level}* in module *{module}*:\n>{message}"

    [global.alerts.discord]
    # Values from temp_config lines 75-79
    enabled = true
    log_levels = ["ERROR", "WARN", "INFO", "DEBUG", "TRACE"]
    default_level = "INFO"
    modules = ["rabbitmq", "llm", "storage"]
    message_template = "**{level}** in module **{module}**:\n> {message}"

    [global.alerts.email]
    # Values from temp_config lines 82-87
    enabled = true
    log_levels = ["ERROR", "WARN", "INFO", "DEBUG", "TRACE"]
    default_level = "INFO"
    modules = ["rabbitmq", "llm", "storage"]
    subject_template = "Prisma AI Alert: {level} in {module}"
    body_template = "Level: {level}\nModule: {module}\nTimestamp: {timestamp}\n\nMessage:\n{message}"

# SurrealDB Configuration (migrated from config.yaml)
[database]
host = "localhost"
port = 8000
username = "root"
password = "root"
namespace = "prisma"
database = "prisma"
use_http = true  # Use HTTP protocol instead of WebSocket
version = "2.2.1"

# RabbitMQ Configuration (latest version)
[rabbitmq]
host = "localhost"
port = 5672
username = "guest"
password = "guest"
vhost = "/"
connection_name = "prisma_alerts"
version = "4.0.4"  # Latest stable version
management_port = 15672

  [rabbitmq.metrics]
  # Values from temp_config lines 54-75
  connection_metrics_enabled = true
  connection_latency_buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
  tls_metrics_enabled = true
  channel_metrics_enabled = true
  channel_latency_buckets = [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5]
  message_metrics_enabled = true
  message_size_buckets = [64.0, 256.0, 1024.0, 4096.0, 16384.0, 65536.0]
  message_processing_buckets = [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
  queue_metrics_enabled = true
  queue_size_enabled = true
  queue_memory_enabled = true
  queue_consumer_enabled = true
  queue_growth_rate_enabled = true
  exchange_metrics_enabled = true
  exchange_binding_enabled = true
  routing_metrics_enabled = true
  consumer_metrics_enabled = true
  consumer_latency_buckets = [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
  consumer_utilization_enabled = true
  consumer_lag_enabled = true

  [rabbitmq.alerts]
  # Values from temp_config lines 156-162
  enabled = true
  log_levels = ["ERROR", "WARN", "INFO", "DEBUG", "TRACE"]
  alert_on_connection_issues = true
  alert_on_channel_issues = true
  alert_on_queue_issues = true
  alert_on_consumer_issues = true
  alert_on_publisher_issues = true

    [rabbitmq.alerts.thresholds]
    # Values from temp_config lines 165-168
    connection_timeout_ms = 5000
    queue_full_percentage = 80
    message_rate_threshold = 1000
    consumer_lag_threshold = 100

  [rabbitmq.telemetry]
  # Values from temp_config lines 171-178
  connection_name = "prisma_ai_rabbitmq"
  enable_connection_tracing = true
  enable_channel_tracing = true
  enable_queue_tracing = true
  enable_exchange_tracing = true
  enable_consumer_tracing = true
  enable_publisher_tracing = true
  sampling_rate = 1.0

    [rabbitmq.telemetry.metrics]
    # Values from temp_config lines 181-200
    connection_metrics_enabled = true
    connection_latency_buckets = [0.1, 0.5, 1.0, 2.0, 5.0]
    tls_metrics_enabled = true
    channel_metrics_enabled = true
    channel_latency_buckets = [0.05, 0.1, 0.25, 0.5, 1.0]
    message_metrics_enabled = true
    message_size_buckets = [1024, 4096, 16384, 65536, 262144]
    message_processing_buckets = [0.01, 0.05, 0.1, 0.5, 1.0]
    queue_metrics_enabled = true
    queue_size_enabled = true
    queue_memory_enabled = true
    queue_consumer_enabled = true
    queue_growth_rate_enabled = true
    exchange_metrics_enabled = true
    exchange_binding_enabled = true
    routing_metrics_enabled = true
    consumer_metrics_enabled = true
    consumer_latency_buckets = [0.01, 0.05, 0.1, 0.25, 0.5]
    consumer_utilization_enabled = true
    consumer_lag_enabled = true

    [rabbitmq.telemetry.logging]
    # Values from temp_config lines 203-204
    min_level = "INFO"
    stream_name = "rabbitmq"

      [rabbitmq.telemetry.logging.components]
      # Values from temp_config lines 207-214
      connection = true
      channel = true
      queue = true
      exchange = true
      publish = true
      consume = true
      routing = true
      performance = true

      [rabbitmq.telemetry.logging.labels]
      # Values from temp_config lines 217-218
      service = "rabbitmq"
      environment = "development"

      [rabbitmq.telemetry.logging.performance]
      # Values from temp_config lines 221-225
      connection_warning_threshold_ms = 1000
      channel_warning_threshold_ms = 500
      queue_warning_threshold_ms = 200
      publish_warning_threshold_ms = 100
      consume_warning_threshold_ms = 100

  [rabbitmq.dashboards]
  # Values from temp_config lines 228-237
  overview_refresh = "10s"
  overview_tags = ["rabbitmq", "overview"]
  connection_refresh = "5s"
  connection_tags = ["rabbitmq", "connections"]
  performance_refresh = "10s"
  performance_tags = ["rabbitmq", "performance"]
  queue_exchange_refresh = "5s"
  queue_exchange_tags = ["rabbitmq", "queues", "exchanges"]
  error_refresh = "10s"
  error_tags = ["rabbitmq", "errors"]

[telemetry]
# Value from temp_config line 24
grafana_service_token = "glsa_Go16KlrwTtqtNmeymEg56hIysMVEHyVg_8be567e3"

  # Endpoints definition duplicated here as per TelemetryConfig struct (using same values as global)
  [telemetry.endpoints]
  prometheus_enabled = true
  prometheus = "http://*************:30158"
  grafana_enabled = true
  grafana = "http://*************:30455"
  loki_enabled = true
  loki = "http://*************:32278/loki/api/v1/push"

  # Env settings duplicated here as per TelemetryConfig struct (using same values as global)
  [telemetry.env_settings]
  service_name = "prisma-pipeline"
  environment = "development"
  service_id = "sa-1-prisma-pipeline"

  # RabbitMQ telemetry configuration is now OPTIONAL
  # Basic telemetry (metrics/logs) goes directly to Prometheus/Loki
  # Uncomment below only if you need advanced features like real-time alerts
  # [telemetry.rabbitmq]
  # host = "localhost"
  # port = 5672
  # username = "guest"
  # password = "guest"
  # vhost = "/"
  # connection_name = "prisma_telemetry"
  # enable_connection_metrics = true
  # enable_channel_metrics = true
  # enable_message_metrics = true
  # sampling_rate = 1.0

    [telemetry.rabbitmq.metrics]
    # Values from temp_config lines 181-200 (merged with defaults for missing keys)
    connection_metrics_enabled = true
    connection_latency_buckets = [0.1, 0.5, 1.0, 2.0, 5.0]
    tls_metrics_enabled = true
    channel_metrics_enabled = true
    channel_latency_buckets = [0.05, 0.1, 0.25, 0.5, 1.0]
    message_metrics_enabled = true
    message_size_buckets = [1024.0, 4096.0, 16384.0, 65536.0, 262144.0]
    message_processing_buckets = [0.01, 0.05, 0.1, 0.5, 1.0]
    queue_metrics_enabled = true
    queue_size_enabled = true
    queue_memory_enabled = true
    queue_consumer_enabled = true
    queue_growth_rate_enabled = true
    exchange_metrics_enabled = true
    exchange_binding_enabled = true
    routing_metrics_enabled = true
    consumer_metrics_enabled = true
    consumer_latency_buckets = [0.01, 0.05, 0.1, 0.25, 0.5]
    consumer_utilization_enabled = true
    consumer_lag_enabled = true

    [telemetry.rabbitmq.telemetry]
    # Values from temp_config lines 171-178
    connection_name = "prisma_ai_rabbitmq"
    enable_connection_tracing = true
    enable_channel_tracing = true
    enable_queue_tracing = true
    enable_exchange_tracing = true
    enable_consumer_tracing = true
    enable_publisher_tracing = true
    sampling_rate = 1.0

      [telemetry.rabbitmq.telemetry.metrics]
      connection_metrics_enabled = true
      connection_latency_buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
      tls_metrics_enabled = true
      channel_metrics_enabled = true
      channel_latency_buckets = [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5]
      message_metrics_enabled = true
      message_size_buckets = [64.0, 256.0, 1024.0, 4096.0, 16384.0, 65536.0]
      message_processing_buckets = [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
      queue_metrics_enabled = true
      queue_size_enabled = true
      queue_memory_enabled = true
      queue_consumer_enabled = true
      queue_growth_rate_enabled = true
      exchange_metrics_enabled = true
      exchange_binding_enabled = true
      routing_metrics_enabled = true
      consumer_metrics_enabled = true
      consumer_latency_buckets = [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
      consumer_utilization_enabled = true
      consumer_lag_enabled = true

      [telemetry.rabbitmq.telemetry.logging]
      min_level = "INFO"
      stream_name = "rabbitmq"

        [telemetry.rabbitmq.telemetry.logging.components]
        connection = true
        channel = true
        queue = true
        exchange = true
        publish = true
        consume = true
        routing = true
        performance = true

        [telemetry.rabbitmq.telemetry.logging.labels]
        service = "rabbitmq"
        environment = "development"

        [telemetry.rabbitmq.telemetry.logging.performance]
        connection_warning_threshold_ms = 1000
        channel_warning_threshold_ms = 500
        queue_warning_threshold_ms = 200
        publish_warning_threshold_ms = 100
        consume_warning_threshold_ms = 100

    [telemetry.rabbitmq.alerts]
    enabled = true
    log_levels = ["ERROR", "WARN", "INFO", "DEBUG", "TRACE"]
    alert_on_connection_issues = true
    alert_on_channel_issues = true
    alert_on_queue_issues = true
    alert_on_consumer_issues = true
    alert_on_publisher_issues = true

      [telemetry.rabbitmq.alerts.thresholds]
      connection_timeout_ms = 5000
      queue_full_percentage = 80
      message_rate_threshold = 1000
      consumer_lag_threshold = 100

    [telemetry.rabbitmq.dashboards]
    overview_refresh = "10s"
    overview_tags = ["rabbitmq", "overview"]
    connection_refresh = "5s"
    connection_tags = ["rabbitmq", "connections"]
    performance_refresh = "10s"
    performance_tags = ["rabbitmq", "performance"]
    queue_exchange_refresh = "5s"
    queue_exchange_tags = ["rabbitmq", "queues", "exchanges"]
    error_refresh = "10s"
    error_tags = ["rabbitmq", "errors"]

  [telemetry.surrealdb]
  # Values from temp_config lines 245-250
  connection_name = "prisma_ai_surrealdb"
  enable_query_tracing = true
  enable_transaction_tracing = true
  enable_document_tracing = true
  enable_index_tracing = true
  sampling_rate = 1.0

    [telemetry.surrealdb.metrics]
    # Values from temp_config lines 253-307 (with defaults added previously)
    basic_metrics_enabled = true
    operation_latency_buckets = [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0]
    query_metrics_enabled = true
    query_latency_buckets = [0.001, 0.005, 0.01, 0.05, 0.1]
    query_complexity_enabled = true
    query_cache_enabled = true
    query_cache_buckets = [10.0, 100.0, 1000.0, 10000.0]
    storage_metrics_enabled = true
    storage_space_enabled = true
    storage_buckets = [1024.0, 10240.0, 102400.0, 1048576.0]
    memory_metrics_enabled = true
    memory_pressure_enabled = true
    gc_metrics_enabled = true
    memory_buckets = [64.0, 128.0, 256.0, 512.0, 1024.0]
    transaction_metrics_enabled = true
    transaction_latency_buckets = [0.001, 0.005, 0.01, 0.05, 0.1]
    rollback_metrics_enabled = true
    lock_contention_enabled = true
    lock_buckets = [0.001, 0.005, 0.01, 0.05, 0.1]
    rag_metrics_enabled = true
    rag_latency_buckets = [0.001, 0.005, 0.01, 0.05, 0.1]
    embedding_metrics_enabled = true
    vector_search_enabled = true
    document_processing_enabled = true
    embedding_latency_buckets = [0.01, 0.05, 0.1, 0.5, 1.0]
    vector_search_buckets = [0.001, 0.005, 0.01, 0.05, 0.1]
    document_processing_buckets = [0.01, 0.05, 0.1, 0.5, 1.0]
    document_metrics_enabled = true
    document_count_enabled = true
    index_metrics_enabled = true
    index_size_enabled = true
    index_operation_buckets = [0.01, 0.1, 1.0, 10.0]

    [telemetry.surrealdb.logging]
    # Using defaults as not fully specified in temp_config
    min_level = "INFO"
    stream_name = "surrealdb_logs"
      [telemetry.surrealdb.logging.components]
      query = true
      transaction = true
      document = false
      index = false
      authentication = true
      performance = true
      rag = true
      [telemetry.surrealdb.logging.labels]
      service = "prisma_ai"
      environment = "development"
      [telemetry.surrealdb.logging.query]
      log_slow_queries = true
      slow_query_threshold_ms = 1000
      log_query_plans = false
      log_query_cache_events = false
      [telemetry.surrealdb.logging.transaction]
      log_begin_commit = true
      log_rollbacks = true
      log_deadlocks = true
      log_lock_wait_time = true
      [telemetry.surrealdb.logging.document]
      log_mutations = false
      log_batch_operations = true
      log_validation_errors = true
      [telemetry.surrealdb.logging.index]
      log_builds = true
      log_updates = false
      log_analysis = false
      [telemetry.surrealdb.logging.rag]
      log_embeddings = true
      log_vector_searches = true
      log_document_processing = true
      [telemetry.surrealdb.logging.performance]
      # Values from temp_config lines 282-286
      query_warning_threshold_ms = 500
      transaction_warning_threshold_ms = 1000
      index_warning_threshold_ms = 200
      embedding_warning_threshold_ms = 300
      vector_search_warning_threshold_ms = 100
      document_processing_warning_threshold_ms = 500
      [telemetry.surrealdb.logging.loki]
      # Values from temp_config lines 289-291
      endpoint = "http://*************:32278/loki/api/v1/push"
      batch_size = 1000
      batch_wait_ms = 1000
      retention_days = 7
        [telemetry.surrealdb.logging.loki.labels]
        # Values from temp_config lines 294-295
        app = "prisma_ai"
        component = "surrealdb"

    [telemetry.surrealdb.dashboards]
    # Values from temp_config lines 298-309
    overview_refresh = "10s"
    overview_tags = ["surrealdb", "overview"]
    performance_refresh = "5s"
    performance_tags = ["surrealdb", "performance"]
    query_refresh = "5s"
    query_tags = ["surrealdb", "queries"]
    storage_refresh = "10s"
    storage_tags = ["surrealdb", "storage"]
    rag_refresh = "10s"
    rag_tags = ["surrealdb", "rag", "vector"]
    error_refresh = "5s"
    error_tags = ["surrealdb", "errors"]

    [telemetry.surrealdb.alerts]
    # Values from temp_config lines 312-317
    enabled = true
    log_levels = ["ERROR", "WARN", "INFO"]
    alert_on_slow_queries = true
    alert_on_transaction_issues = true
    alert_on_authentication_issues = true
    alert_on_performance_warnings = true

      [telemetry.surrealdb.alerts.thresholds]
      # Values from temp_config lines 320-324
      slow_query_ms = 1000
      transaction_timeout_ms = 5000
      connection_timeout_ms = 3000
      memory_usage_percentage = 90
      storage_usage_percentage = 85

  [telemetry.llm]
  # Values from temp_config lines 331-334
  enable_inference_tracing = true
  enable_lora_tracing = true
  enable_embedding_tracing = true
  sampling_rate = 1.0

    [telemetry.llm.tracing]
    # Values from temp_config lines 337-341
    model_loading = true
    inference = true
    tokenization = true
    embedding = true
    lora = true

      [telemetry.llm.tracing.attributes]
      # Values from temp_config lines 344-345
      service = "prisma_ai"
      environment = "development"

    [telemetry.llm.metrics]
    # Values from temp_config lines 348-355
    model_performance_enabled = true
    resource_usage_enabled = true
    lora_metrics_enabled = true
    embedding_metrics_enabled = true
    inference_latency_buckets = [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0]
    batch_size_buckets = [1.0, 4.0, 8.0, 16.0, 32.0, 64.0]
    memory_usage_buckets = [1024.0, 4096.0, 16384.0, 65536.0, 262144.0]
    tokens_per_second_buckets = [1.0, 10.0, 50.0, 100.0, 500.0, 1000.0]

    [telemetry.llm.logging]
    # Values from temp_config lines 358-359
    min_level = "INFO"
    stream_name = "llm"

      [telemetry.llm.logging.components]
      # Values from temp_config lines 362-367
      inference = true
      lora = true
      embedding = true
      tokenization = true
      performance = true
      resources = true

      [telemetry.llm.logging.labels]
      # Values from temp_config lines 370-371
      service = "llm"
      environment = "development"

      [telemetry.llm.logging.performance]
      # Values from temp_config lines 374-377
      inference_warning_threshold_ms = 1000
      lora_warning_threshold_ms = 500
      embedding_warning_threshold_ms = 200
      tokenization_warning_threshold_ms = 100

    [telemetry.llm.dashboards]
    # Values from temp_config lines 380-389
    overview_refresh = "10s"
    overview_tags = ["llm", "overview"]
    model_performance_refresh = "5s"
    model_performance_tags = ["llm", "performance", "inference"]
    resource_usage_refresh = "10s"
    resource_usage_tags = ["llm", "resources", "utilization"]
    lora_refresh = "5s"
    lora_tags = ["llm", "lora", "adapters"]
    embedding_refresh = "10s"
    embedding_tags = ["llm", "embeddings", "vectors"]

    [telemetry.llm.alerts]
    # Values from temp_config lines 392-393
    enabled = true
    log_levels = ["ERROR", "WARN", "INFO"]

      [telemetry.llm.alerts.performance]
      # Values from temp_config lines 396-399
      enabled = true
      high_latency_threshold_ms = 1000
      low_throughput_threshold_tokens = 100
      resource_exhaustion_percentage = 90

      [telemetry.llm.alerts.errors]
      # Values from temp_config lines 402-406
      enabled = true
      alert_on_model_loading = true
      alert_on_inference_failures = true
      alert_on_out_of_memory = true
      max_consecutive_failures = 3

      [telemetry.llm.alerts.resources]
      # Values from temp_config lines 409-413
      enabled = true
      gpu_memory_threshold_percentage = 85
      cpu_utilization_threshold_percentage = 80
      system_memory_threshold_percentage = 90
      monitoring_interval_seconds = 30

      [telemetry.llm.alerts.quality]
      # Values from temp_config lines 416-420
      enabled = true
      token_error_threshold_percentage = 5
      embedding_quality_threshold = 0.8
      context_window_usage_percentage = 90
      max_invalid_tokens_per_batch = 10

  [telemetry.system_error]
  # Values from temp_config lines 99
  enabled = true

    [telemetry.system_error.logging]
    # Values from temp_config lines 102-105
    min_level = "INFO"
    include_stacktrace = true
    include_metadata = true
    retention_days = 30

    [telemetry.system_error.metrics]
    # Values from temp_config lines 108-111
    enabled = true
    error_rate_window_minutes = 60
    max_stored_errors = 1000
    histogram_buckets = [0.1, 0.5, 1.0, 2.0, 5.0]

    [telemetry.system_error.alerts]
    # Values from temp_config lines 114-120
    enabled = true
    alert_on_critical = true
    alert_on_error = true
    alert_on_warning = false
    error_rate_threshold = 10.0
    error_burst_threshold = 5
    error_burst_window_seconds = 60

    [telemetry.system_error.tracing]
    # Values from temp_config lines 123-131
    enabled = true
    include_context = true
    include_stack_trace = true
    propagate_correlation_id = true
    sampling_rate = 1.0
    error_group_window_seconds = 300
    circuit_breaker_events = true
    retry_events = true
    timeout_events = true

    [telemetry.system_error.dashboards]
    # Values from temp_config lines 134-149
    enabled = true
    refresh_interval = "10s"
    error_dashboard_tags = ["errors", "system", "monitoring"]
    circuit_breakers_dashboard_tags = ["resilience", "circuit-breakers", "system"]
    retries_dashboard_tags = ["resilience", "retries", "system"]
    error_distribution_dashboard_tags = ["errors", "distribution", "system"]
    error_timeline_dashboard_tags = ["errors", "timeline", "system"]
    error_severity_distribution_refresh = "30s"
    error_source_distribution_refresh = "30s"
    error_rate_timeline_refresh = "10s"
    circuit_breaker_status_refresh = "5s"
    retry_count_refresh = "15s"
    service_health_refresh = "5s"
    error_counts_by_service_refresh = "15s"
    error_counts_by_type_refresh = "20s"
    top_errors_refresh = "30s"
