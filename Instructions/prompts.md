I have the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/generics.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/mod.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/prisma_engine.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/traits.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/types.rs module and I would you to analyze it and implement tests in the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/prisma_engine_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration_tests.rs ( entry point) files. the test will NOT use mocks or placeholders but real methods/components. the following tests to implement will be - 
#### Test: `test_long_running_stability`
**Purpose**: Test system stability over extended periods
**Components**: Full system, continuous operation
**Test Duration**: 24+ hours
**Monitoring**:
- Performance degradation
- Memory growth
- Error accumulation
- Resource exhaustion

Keep top-level imports minimal - Only types, no traits
Import traits locally - Within {} blocks where needed
Use helper functions - For creating monitors without trait dependencies
One trait per test block - Avoid mixing conflicting traits in same scope
Test-specific imports - Import only what each test actually uses
Don't replace current test/s, add new tests to the bottom of the file

NOTE : the llama API ( /Users/<USER>/Documents/prisma_workspace/prisma_ai/external/llama.cpp/include/llama.h ) has been updated, make sure it matched the rust code ( /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/llm/implementation
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/llm/interface ) to prevent any SIGSEGV or any errors and have a comprehensive test.

if needed -   # all Configurations can be found in - /Users/<USER>/Documents/prisma_workspace/configs/config.yaml


Follow the same pattern as the previous tests - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/decision_maker_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/execution_strategies_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/executor_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/agent_manager_tests.rs
	





ok, instead of implementing, instead 1 - verify and 2 plan
Verify : I want to test on the docker on the server that will be used for deployment and not the local host configorations - /Users/<USER>/Documents/prisma_workspace/configs/config.yaml , now I have k3 on ssh db@*************, with grafna, prometheus and loki but verify first. i have the server on and i can access grafna at http://*************:30455/?orgId=1&from=now-6h&to=now&timezone=browser, ( i also have this) Prometheus dashboard	http://*************:30158				
Grafna Dashboard	http://*************:30455/login				
Loki end point	http://*************:32278/loki/api/v1/push	curl -X GET "http://*************:32278/loki/api/v1/labels"	). the full config can be found at - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/config/settings.toml, which i think should be used instead of /Users/<USER>/Documents/prisma_workspace/configs/config.yaml. instead let /Users/<USER>/Documents/prisma_workspace/configs/config.yaml and be used for authentication ( /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma_ui/services/auth_service.rs, at a later date we'll implement auth, for now its dealing with telemetry) and surreldb config ( which is temporatily using a localhost) so verify at ssh db@************* that all pods ar running and updated ( on server and crates) the linux ubuntu server os will also need to be updated so the pods may have to be stopped. i also have rabbitmq as a data source at http://*************:30455/connections/datasources but not sure if its a k3 pod or  if it needs to be		

2 - plan the in you tasks list the how we're going to approch all these and in the end as you suggestd - "Add Loki subscriber to TelemetryCore::init()
Test with Docker Loki running
Verify in Grafana that logs appear" and lastly add to the plan " Phase 1: Alert Integration
Extend telemetry/alerts/ modules with health-specific alerts
Add alert processing to existing health service methods
Integrate with existing telemetry publishing loops
Phase 2: Dashboard Integration
Extend telemetry/dashboards/ modules with health dashboards
Add dashboard generation to health services
Integrate with Grafana via existing telemetry core
Phase 3: Advanced Features
Alert escalation and notification routing
Custom dashboard creation and management
Alert correlation and noise reduction"
	






