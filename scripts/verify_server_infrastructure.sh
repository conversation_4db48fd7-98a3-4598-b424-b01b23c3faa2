#!/bin/bash
# =================================================================================================
# Server Infrastructure Verification Script
# =================================================================================================
# Purpose: Verify K3s cluster, telemetry services, and server status on *************
# Usage: Run this script on the server via SSH: ssh db@************* 'bash -s' < verify_server_infrastructure.sh
# =================================================================================================

echo "🔍 PRISMA AI - SERVER INFRASTRUCTURE VERIFICATION"
echo "=================================================="
echo "Server: $(hostname -I | awk '{print $1}')"
echo "Date: $(date)"
echo "User: $(whoami)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo "1. 🖥️  SYSTEM STATUS"
echo "==================="

# Check OS version
echo "OS Version:"
lsb_release -a 2>/dev/null || cat /etc/os-release | head -5
echo ""

# Check system updates
print_info "Checking for system updates..."
apt list --upgradable 2>/dev/null | wc -l | xargs -I {} echo "Available updates: {}"
echo ""

# Check disk space
echo "Disk Usage:"
df -h / | tail -1 | awk '{print "Root partition: " $3 " used / " $2 " total (" $5 " used)"}'
echo ""

# Check memory
echo "Memory Usage:"
free -h | grep Mem | awk '{print "Memory: " $3 " used / " $2 " total"}'
echo ""

echo "2. 🐳 K3S CLUSTER STATUS"
echo "======================="

# Check if K3s is running
systemctl is-active --quiet k3s
print_status $? "K3s service is running"

# Check K3s version
if command -v k3s &> /dev/null; then
    echo "K3s version: $(k3s --version | head -1)"
else
    print_warning "K3s command not found in PATH"
fi
echo ""

# Check kubectl access
if sudo kubectl get nodes &>/dev/null; then
    print_status 0 "kubectl access working"
    echo "Cluster nodes:"
    sudo kubectl get nodes -o wide
else
    print_status 1 "kubectl access failed"
fi
echo ""

echo "3. 📊 TELEMETRY SERVICES STATUS"
echo "==============================="

# Check pods in all namespaces
print_info "Checking telemetry-related pods..."
if sudo kubectl get pods --all-namespaces &>/dev/null; then
    echo "All pods status:"
    sudo kubectl get pods --all-namespaces | grep -E "(grafana|prometheus|loki|rabbitmq)" || echo "No telemetry pods found with standard names"
    echo ""
    
    echo "All running pods:"
    sudo kubectl get pods --all-namespaces --field-selector=status.phase=Running | head -10
else
    print_status 1 "Cannot access pod information"
fi
echo ""

echo "4. 🌐 SERVICE ENDPOINTS VERIFICATION"
echo "===================================="

# Test Grafana
print_info "Testing Grafana endpoint..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:30455/api/health | grep -q "200"; then
    print_status 0 "Grafana (port 30455) is responding"
else
    print_status 1 "Grafana (port 30455) is not responding"
fi

# Test Prometheus
print_info "Testing Prometheus endpoint..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:30158/api/v1/status/config | grep -q "200"; then
    print_status 0 "Prometheus (port 30158) is responding"
else
    print_status 1 "Prometheus (port 30158) is not responding"
fi

# Test Loki
print_info "Testing Loki endpoint..."
if curl -s "http://localhost:32278/loki/api/v1/labels" | grep -q "success"; then
    print_status 0 "Loki (port 32278) is responding"
    echo "Loki labels: $(curl -s "http://localhost:32278/loki/api/v1/labels" | head -c 100)..."
else
    print_status 1 "Loki (port 32278) is not responding"
fi
echo ""

echo "5. 🐰 RABBITMQ STATUS"
echo "===================="

# Check if RabbitMQ is running as a service
if systemctl is-active --quiet rabbitmq-server; then
    print_status 0 "RabbitMQ system service is running"
    echo "RabbitMQ version: $(rabbitmqctl version 2>/dev/null | head -1 || echo 'Version check failed')"
elif sudo kubectl get pods --all-namespaces | grep -q rabbitmq; then
    print_status 0 "RabbitMQ found as K3s pod"
    sudo kubectl get pods --all-namespaces | grep rabbitmq
else
    print_warning "RabbitMQ status unclear - not found as service or pod"
fi

# Test RabbitMQ management interface (common ports)
for port in 15672 31672 30672; do
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$port | grep -q "200"; then
        print_status 0 "RabbitMQ management interface responding on port $port"
        break
    fi
done
echo ""

echo "6. 🔧 NETWORK CONFIGURATION"
echo "==========================="

# Check listening ports
print_info "Key listening ports:"
netstat -tlnp 2>/dev/null | grep -E "(30455|30158|32278|5672|15672)" | head -10 || ss -tlnp | grep -E "(30455|30158|32278|5672|15672)" | head -10
echo ""

# Check firewall status
if command -v ufw &> /dev/null; then
    echo "UFW Status:"
    ufw status
elif command -v iptables &> /dev/null; then
    echo "Iptables rules (first 5):"
    iptables -L | head -10
fi
echo ""

echo "7. 📋 SUMMARY & RECOMMENDATIONS"
echo "==============================="

print_info "Infrastructure verification complete!"
echo ""
echo "Next steps:"
echo "1. If system updates are available, consider updating: sudo apt update && sudo apt upgrade"
echo "2. Verify all telemetry services are accessible from external network"
echo "3. Check RabbitMQ configuration if it's needed for telemetry"
echo "4. Ensure K3s cluster is stable before proceeding with telemetry integration"
echo ""
echo "Endpoints to verify from external network:"
echo "- Grafana: http://*************:30455"
echo "- Prometheus: http://*************:30158"
echo "- Loki: http://*************:32278/loki/api/v1/labels"
echo ""
echo "🎯 Ready for telemetry integration if all services are green!"
