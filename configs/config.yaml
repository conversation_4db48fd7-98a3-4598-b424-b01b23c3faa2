# PrismaAI Configuration

# Service Credentials
services:
  # RabbitMQ Configuration (for auth service)
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    vhost: /
    management_port: 15672
    version: 4.0.4  # Latest stable version

# Authentication Configuration (for future auth_service.rs implementation)
auth:
  enabled: false
  jwt_secret: "your-jwt-secret-here"
  session_timeout_hours: 24

# Future authentication providers
auth_providers:
  email_password:
    enabled: true
    require_email_verification: false
  oauth:
    enabled: false
    providers: []