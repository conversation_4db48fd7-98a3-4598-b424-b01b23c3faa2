# 🚀 Prisma AI Telemetry Integration Plan

## 📊 **Current Status**
- ✅ Health services implemented and tested (13 tests passing)
- ✅ Server infrastructure verified (<PERSON><PERSON>, Prometheus, Loki running at *************)
- ✅ Configuration consolidation completed (SurrealDB moved to settings.toml)
- ✅ Settings configuration loader created (settings_config.rs)
- ❌ Server needs 155 system updates (Ubuntu 24.04 LTS)
- ❌ kubectl access permissions need fixing (permission denied)
- ❓ RabbitMQ status unclear (not found as service or pod)

## 🎯 **Implementation Phases**

### **PHASE 0: Infrastructure Verification & Setup**

#### **Task 1: Server Infrastructure Verification** 🔄 *IN PROGRESS*
- **Objective**: Complete verification of K3s cluster and services
- **Actions**:
  - Run verification script on server: `ssh db@************* 'bash -s' < scripts/verify_server_infrastructure.sh`
  - Check all pods status: `sudo kubectl get pods --all-namespaces`
  - Verify system updates needed: `apt list --upgradable`
  - Document current infrastructure state
- **Success Criteria**: All telemetry services responding, cluster healthy

#### **Task 2: Configuration Consolidation** ✅ *COMPLETED*
- **Objective**: Establish clear config separation
- **Actions Completed**:
  - ✅ Moved SurrealDB config from `config.yaml` to `settings.toml`
  - ✅ Created `settings_config.rs` with structured configuration loading
  - ✅ Updated `config/mod.rs` to expose settings configuration
  - ✅ Verified telemetry endpoints point to server (*************)
- **Result**: Clean config separation achieved, settings.toml is primary config

#### **Task 3: RabbitMQ Service Verification**
- **Objective**: Determine RabbitMQ deployment status
- **Actions**:
  - Check if RabbitMQ is K3s pod or separate service
  - Verify connectivity to RabbitMQ management interface
  - Test message queue functionality
  - Update configuration accordingly
- **Success Criteria**: RabbitMQ status confirmed and accessible

---

### **PHASE 1: Core Telemetry Integration**

#### **Task 4: Loki Integration Implementation**
- **Objective**: Enable automatic log forwarding to Loki
- **Actions**:
  - Create `LokiLayer` tracing subscriber in `telemetry/logging/subscriber.rs`
  - Update `TelemetryCore::init()` to include Loki subscriber
  - Configure log filtering (health_telemetry, component_health_telemetry targets)
  - Test log forwarding with health service logs
- **Implementation**:
  ```rust
  // Add to TelemetryCore::init()
  let loki_config = LokiConfig::from_settings(&self.config);
  let loki_client = LokiClient::new(loki_config);
  let loki_layer = LokiLayer::new(loki_client);
  
  tracing_subscriber::registry()
      .with(EnvFilter::new("info"))
      .with(fmt::layer())
      .with(loki_layer) // ✅ Automatic Loki integration
      .init();
  ```
- **Success Criteria**: Health service logs appear in Loki automatically

#### **Task 5: Telemetry Integration Testing**
- **Objective**: Verify end-to-end telemetry pipeline
- **Actions**:
  - Run health service tests with server infrastructure
  - Verify logs appear in Loki: `curl "http://*************:32278/loki/api/v1/query?query={target=\"health_telemetry\"}"`
  - Check Grafana can query Loki data source
  - Test metrics collection in Prometheus
  - Validate OpenTelemetry traces
- **Success Criteria**: Complete telemetry pipeline working

---

### **PHASE 2: Alert Integration**

#### **Task 6: Health-Specific Alert System**
- **Objective**: Extend telemetry/alerts/ with health monitoring
- **Actions**:
  - Enhance `telemetry/alerts/system.rs` with health alert types
  - Create `HealthAlertManager` in `telemetry/alerts/health.rs`
  - Integrate alert processing into health service methods
  - Configure alert thresholds and escalation rules
- **Implementation**:
  ```rust
  // In health_service.rs
  impl HealthService {
      async fn process_health_alerts(&self, summary: &EngineHealthSummary) {
          let alert_manager = HealthAlertManager::new(self.telemetry.clone());
          
          for (component, report) in &summary.components {
              if matches!(report.status, HealthStatus::Critical) {
                  alert_manager.send_critical_alert(component, report).await;
              }
          }
      }
  }
  ```
- **Success Criteria**: Automated alerts for critical health events

#### **Task 7: Alert Integration with Telemetry Loop**
- **Objective**: Integrate alerts with existing telemetry publishing
- **Actions**:
  - Add alert processing to health service telemetry publishing loops
  - Configure alert routing (Grafana, email, Slack, etc.)
  - Implement alert correlation to reduce noise
  - Test alert escalation workflows
- **Success Criteria**: Alerts automatically generated and routed

---

### **PHASE 3: Dashboard Integration**

#### **Task 8: Health Dashboard Generation**
- **Objective**: Extend telemetry/dashboards/ with health visualizations
- **Actions**:
  - Enhance `telemetry/dashboards/grafana.rs` with health dashboard templates
  - Create dashboard generation in health services
  - Implement automatic dashboard provisioning
  - Configure health-specific panels and queries
- **Implementation**:
  ```rust
  // In telemetry/dashboards/health.rs
  pub struct HealthDashboardGenerator {
      grafana_client: GrafanaClient,
  }
  
  impl HealthDashboardGenerator {
      pub async fn create_engine_health_dashboard(&self) -> Result<Dashboard> {
          // Generate dashboard JSON for engine health
      }
  }
  ```
- **Success Criteria**: Automated health dashboard creation

#### **Task 9: Grafana Integration via Telemetry Core**
- **Objective**: Integrate dashboard management with existing telemetry
- **Actions**:
  - Connect dashboard generation to TelemetryCore
  - Implement dashboard updates based on health service changes
  - Configure dashboard permissions and sharing
  - Test dashboard provisioning and updates
- **Success Criteria**: Dashboards automatically managed via telemetry core

---

### **PHASE 4: Advanced Features**

#### **Task 10: Alert Escalation & Notification Routing**
- **Objective**: Implement sophisticated alerting workflows
- **Actions**:
  - Create alert escalation policies
  - Implement notification routing (email, Slack, PagerDuty)
  - Add alert acknowledgment and resolution tracking
  - Configure on-call schedules and escalation chains
- **Success Criteria**: Production-ready alerting system

#### **Task 11: Custom Dashboard Creation & Management**
- **Objective**: Enable dynamic dashboard creation
- **Actions**:
  - Implement custom dashboard templates
  - Add dashboard versioning and rollback
  - Create dashboard sharing and collaboration features
  - Implement dashboard performance optimization
- **Success Criteria**: Flexible dashboard management system

#### **Task 12: Alert Correlation & Noise Reduction**
- **Objective**: Implement intelligent alert processing
- **Actions**:
  - Create alert correlation algorithms
  - Implement alert grouping and deduplication
  - Add machine learning for alert pattern recognition
  - Configure alert suppression during maintenance
- **Success Criteria**: Intelligent alert system with minimal noise

---

## 🔧 **Technical Implementation Details**

### **Key Files to Modify:**
- `prisma_ai/src/telemetry/core/mod.rs` - Add Loki subscriber
- `prisma_ai/src/telemetry/logging/subscriber.rs` - New Loki layer
- `prisma_ai/src/telemetry/alerts/health.rs` - Health-specific alerts
- `prisma_ai/src/telemetry/dashboards/health.rs` - Health dashboards
- `prisma_ai/src/config/settings.toml` - Primary telemetry config
- `configs/config.yaml` - Auth and SurrealDB only

### **Configuration Updates:**
```toml
# settings.toml - Primary telemetry configuration
[telemetry.endpoints]
prometheus = "http://*************:30158"
grafana = "http://*************:30455"
loki = "http://*************:32278/loki/api/v1/push"

[telemetry.health]
enable_auto_alerts = true
alert_thresholds.critical_response_time_ms = 5000
alert_thresholds.error_rate_threshold = 0.1
dashboard_auto_provision = true
```

### **Success Metrics:**
- ✅ All health service logs automatically appear in Loki
- ✅ Grafana dashboards show real-time health metrics
- ✅ Alerts automatically generated for critical events
- ✅ End-to-end telemetry pipeline operational
- ✅ Production-ready monitoring and alerting system

## 🚀 **Next Steps**
1. Run server verification script
2. Complete infrastructure setup
3. Implement Loki integration
4. Test end-to-end pipeline
5. Proceed with alert and dashboard phases
